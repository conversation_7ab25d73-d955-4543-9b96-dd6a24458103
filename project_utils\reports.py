import os
from django.db.models import Sum
from fpdf import FPDF
from fpdf.fonts import FontFace
from exams import models
from project_utils import constants, custom_utils
from project_utils.custom_utils import compute_exam_fees, get_current_year
import qrcode
import arabic_reshaper
from bidi.algorithm import get_display


def reshape_text(text):
    return get_display(arabic_reshaper.reshape(text))


class BasePDF(FPDF):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.add_font('TimesNewRoman', '', 'static/fonts/times.ttf', uni=True)
        self.add_font('TimesNewRoman', 'B', 'static/fonts/timesbd.ttf', uni=True)
        self.add_font('TimesNewRoman', 'BI', 'static/fonts/timesbi.ttf', uni=True)
        self.add_font('TimesNewRoman', 'I', 'static/fonts/timesi.ttf', uni=True)

    def set_font(self, family='Times', style= "", size = 0):
        if family.lower() == 'times':
            return super().set_font('TimesNewRoman', style, size)
        return super().set_font(family, style, size)

    def add_first_page_header(self):
        self.set_xy(2, 5)
        self.image('static/images/entete.jpg', w=300, h=63)

    def footer(self):
        self.set_xy(150, -15)
        self.set_font('Times', size=12)
        self.cell(txt=f'{self.page_no()}')


class CandidatesListPDF(BasePDF):
    def add_content(self, queryset=None, location = None, school=None, year=None, exam=''):
        self.add_page()
        self.add_first_page_header()
        self.set_font('Times', 'B', 12)
        self.set_xy(self.x + 35, self.y+5)
        exam = exam.upper()

        title = f'LISTE DES CANDIDATS AU {exam}'
        if location and not school:
            title += f' DE {location}'
        elif school:
            title += f' : ECOLE {school.name} '

        title += f" ({year})"

        self.cell(w=225, h=8, txt=title, 
                border=1, align='C')
        self.set_y(self.y + 15)

        self.set_font_size(12)
        self.set_line_width(0.3)
        headings_style = FontFace(emphasis="BOLD", fill_color=(211, 211, 211))
        with self.table(
            # borders_layout="NO_HORIZONTAL_LINES",
            cell_fill_color=(224, 235, 255),
            col_widths=(10, 27, 80, 11, 18, 35, 18),
            headings_style=headings_style,
            line_height=7,
            text_align=('CENTER', "CENTER", 'LEFT', 'CENTER', 'CENTER', 'CENTER', 'CENTER'),
            width=260,
        ) as table:
            row = table.row()
            row.cell('N°')
            row.cell("NUMERO D'INSCRIPTION")
            row.cell('NOM ET PRENOMS')
            row.cell('SEXE')
            row.cell('NE LE')
            row.cell('LIEU')
            row.cell('STATUT')

            if queryset:
                counter = 1
                for enrollment in queryset:
                    row = table.row()
                    row.cell(f'{str(counter).zfill(2)}')
                    row.cell(enrollment.student.identifier)
                    row.cell(str(enrollment.student.get_full_name()).upper())
                    row.cell(str(enrollment.student.gender))
                    row.cell(f"{enrollment.student.birth_date.strftime('%d/%m/%Y')}")
                    row.cell(str(enrollment.student.birth_place).upper())
                    row.cell('VALIDE' if enrollment.confirmed else 'OUVERT')
                    counter += 1


class SchoolsListPDF(BasePDF):
    def add_content(self, user, year, queryset, location=None, all_candidates=False):
        self.add_page()
        self.add_first_page_header()
        
        self.set_margins(5, 5, 5)
        self.set_font('Times', 'B', 12)
        self.set_xy(self.x + 35, self.y+5)

        title = f'LISTE DES ECOLES CHERIFLA '
        if location:
            title += f'DE {location}'
        elif user.has_perm('add_schoolpayment'):
            title += '- SERVICE FINANCIER'
        
        title += f' ({year})'
        self.cell(w=225, h=8, txt=title, border=1, align='C')
        self.set_y(self.y + 15)

        self.set_font('Times', 'B', 10)
        self.set_line_width(0.3)
        grayscale = 210
        headings_style = FontFace(emphasis="BOLD", fill_color=(211, 211, 211))
        text_aligns = (
            'CENTER', "LEFT", 'LEFT', 'LEFT', 'LEFT', 'CENTER', 'CENTER', 
            'CENTER', 'CENTER', 'CENTER', 'CENTER', 'CENTER', 'CENTER', 'CENTER',
        )
        col_widths = (7, 15, 50, 30, 22, 30, 11, 11, 11, 11, 11, 11, 11, 11)

        if not location:
            col_widths = (7, 15, 40, 25, 22, 22, 25, 11, 11, 11, 11, 11, 11, 11, 11)
            text_aligns = (
                'CENTER', "LEFT", 'LEFT', 'LEFT', 'LEFT', 
                'LEFT', 'CENTER', 'CENTER', 'CENTER', 'CENTER',
                'CENTER', 'CENTER', 'CENTER', 'CENTER', 'CENTER',
            )
        with self.table(
            cell_fill_color=grayscale,
            col_widths=col_widths,
            headings_style=headings_style,
            line_height=8,
            text_align=text_aligns,
            width=self.w - 20,
            num_heading_rows=2
        ) as table:
            row = table.row(style=headings_style)
            row.cell('N°', rowspan=2)
            row.cell('CODE', rowspan=2)
            row.cell("ECOLE", rowspan=2)
            row.cell('DIRECTEUR', rowspan=2)
            row.cell('CONTACT', rowspan=2)
            if not location:
                row.cell('LOCALITE', rowspan=2)
            row.cell('DRENA', rowspan=2)
            row.cell('CANDIDATS INSCRITS', colspan=4)
            row.cell('CANDIDATS VALIDES', colspan=4)
            
            row = table.row(style=headings_style)
            row.cell('CEPE')
            row.cell('BEPC')
            row.cell('BAC')
            row.cell('TOT.')
            row.cell('CEPE')
            row.cell('BEPC')
            row.cell('BAC')
            row.cell('TOT.')

            is_valid = True
            if queryset:
                counter = 1
                for school in queryset:
                    if not all_candidates:
                        is_valid = school.students_enrolled > 0

                    if is_valid:
                        row = table.row()
                        row.cell(f'{str(counter).zfill(2)}')
                        row.cell(school.identifier)
                        row.cell(str(school.name).upper())
                        row.cell(str(school.director).upper())
                        row.cell(school.phone)
                        if not location:
                            row.cell(f'{school.local_commission}')
                        row.cell(f"{school.drena or '-'}")
                        row.cell(f"{str(school.cepe_enrolled).zfill(2)}")
                        row.cell(f"{str(school.bepc_enrolled).zfill(2)}")
                        row.cell(f"{str(school.bac_enrolled).zfill(2)}")
                        row.cell(f"{str(school.students_enrolled).zfill(2)}")
                        row.cell(f"{str(school.cepe_students_count).zfill(2)}")
                        row.cell(f"{str(school.bepc_students_count).zfill(2)}")
                        row.cell(f"{str(school.bac_students_count).zfill(2)}")
                        row.cell(f"{str(school.students_count).zfill(2)}")
                        counter += 1


class PaymentsList(BasePDF):
    def add_content(self, user, queryset, year, location=None):
        self.add_page()
        self.add_first_page_header()

        self.set_font('Times', 'B', 14)
        self.set_xy(self.x + 35, self.y+5)

        title = f'RESUME DES VERSEMENTS '
        if location:
            title += f'DE {location}'
        elif user.has_perm('add_schoolpayment'):
            title += '- SERVICE FINANCIER'
        title += f' ({year})'
        self.cell(w=225, h=8, txt=title, border=1, align='C')
        self.set_y(self.y + 15)

        self.set_font_size(12)
        self.set_line_width(0.3)
        headings_style = FontFace(emphasis="BOLD", fill_color=(211, 211, 211))
        text_aligns = ('CENTER', "LEFT", 'LEFT', 'CENTER', 'CENTER', 'CENTER', 'CENTER', 'CENTER', 'CENTER')
        col_widths = (10, 60, 25, 20, 20, 20, 30, 30, 30)

        with self.table(
            cell_fill_color=(224, 235, 255),
            col_widths=col_widths,
            headings_style=headings_style,
            line_height=8,
            text_align=text_aligns,
            width=260,
        ) as table:
            row = table.row()
            row.cell('N°')
            row.cell('ECOLE')
            row.cell("LOCALITE")
            row.cell('CEPE')
            row.cell('BEPC')
            row.cell('BAC')
            row.cell('TOTAL A PAYER')
            row.cell('VERSEMENTS')
            row.cell('RESTE A PAYER')

            if queryset.exists():
                counter = 1
                for school in queryset:
                    row = table.row()
                    row.cell(f'{str(counter).zfill(2)}')
                    row.cell(f'{str(school.name).upper()}')
                    row.cell(f'{school.local_commission}')

                    cepe_fees = compute_exam_fees(school.cepe_students_count, 
                        year=year, exam=constants.EXAM_CEPE) 
                    row.cell("{:,}".format(cepe_fees).replace(',', ' '))
                    bepc_fees = compute_exam_fees(school.bepc_students_count, 
                        year=year, exam=constants.EXAM_BEPC)
                    row.cell("{:,}".format(bepc_fees).replace(',', ' '))
                    bac_fees = compute_exam_fees(school.bac_students_count, 
                        year=year, exam=constants.EXAM_BAC) 
                    row.cell("{:,}".format(bac_fees).replace(',', ' '))

                    school_payments = 0
                    if school.schoolpayment_set.exists():
                        school_payments = school.schoolpayment_set.filter(
                            year=year
                        ).aggregate(total=Sum('amount'))['total']
                    total_fees = cepe_fees + bepc_fees + bac_fees
                    row.cell("{:,}".format(total_fees).replace(',', ' '))
                    row.cell("{:,}".format(school_payments or 0).replace(',', ' '))
                    row.cell("{:,}".format(total_fees - (school_payments or 0)).replace(',', ' '))
                    counter += 1


class FeesListPDF(BasePDF):
    def add_content(self, user, queryset, location=None, year=None):
        self.add_page()
        self.add_first_page_header()

        self.set_font('Times', 'B', 14)
        self.set_xy(self.x + 35, self.y+5)

        title = f'LISTE DES COTISATIONS ANNUELLES DES ECOLES '
        if location:
            title += f'DE {location}'
        elif user.has_perm('add_schoolpayment'):
            title += '- SERVICE FINANCIER'
        
        title += f' ({year})'
        self.cell(w=225, h=8, txt=title, border=1, align='C')
        self.set_y(self.y + 15)

        self.set_font_size(12)
        self.set_line_width(0.3)
        headings_style = FontFace(emphasis="BOLD", fill_color=(211, 211, 211))
        text_aligns = ('CENTER', "LEFT", 'LEFT', 'RIGHT', 'LEFT')
        col_widths = (10, 30, 50, 20, 15)

        with self.table(
            cell_fill_color=(224, 235, 255),
            col_widths=col_widths,
            headings_style=headings_style,
            line_height=8,
            text_align=text_aligns,
            width=260,
        ) as table:
            row = table.row()
            row.cell('N°')
            row.cell('LOCALITE')
            row.cell('ECOLE')
            row.cell("MONTANT")
            row.cell('VERSE LE')

            if queryset:
                counter = 1
                for fee in queryset:
                    row = table.row()
                    row.cell(f'{str(counter).zfill(2)}')
                    row.cell(f'{fee.school.local_commission}')
                    row.cell(f'{fee.school}')
                    row.cell(f'{fee.amount}')
                    row.cell(f"{fee.date.strftime('%d/%m/%Y')}")
                    counter += 1


class CenterCandidatesListPDF(BasePDF):
    def add_content(self, queryset, center, year=None, exam='', room=None, anonymat=False):
        self.add_page()
        self.add_first_page_header()
        self.set_font('Times', 'B', 14)
        x = self.x + 15
        self.set_xy(x, self.y+5)
        exam = exam.upper()

        title = f'LISTE DES CANDIDATS AU {exam.upper()} - SESSION {year.short_name} '
        
        extra = ''
        if room:
            extra = 'LISTE '
        if not room:
            title += f'\n {center} / {center.location} '
        else:
            title += f'\n{extra}{str(room).upper()} - {center}'

        self.multi_cell(w=263, h=8, txt=title, 
                border=1, align='C')
        self.set_xy(17, self.y + 5)
        boys = queryset.filter(student__gender=constants.GENDER_MALE).count()
        girls = queryset.filter(student__gender=constants.GENDER_FEMALE).count()
        students = boys + girls
        self.cell(txt=f'GARCONS : {str(boys).zfill(2)} |  FILLES : {str(girls).zfill(2)} |  TOTAL : {str(students).zfill(2)}')
        self.set_y(self.y + 10)
        self.set_font('Times', '', 12)
        self.set_line_width(0.3)
        headings_style = FontFace(emphasis="BOLD", fill_color=(211, 211, 211))
        with self.table(
            # borders_layout="NO_HORIZONTAL_LINES",
            cell_fill_color=(224, 235, 255),
            col_widths=(10, 24, 27, 70, 13, 25, 45, 35),
            headings_style=headings_style,
            line_height=7,
            text_align=('CENTER', "CENTER", "CENTER", 'LEFT', 'CENTER', 'CENTER', 'CENTER', 'CENTER'),
            width=260,
        ) as table:
            row = table.row()
            row.cell('N°')
            row.cell("N° TABLE")
            row.cell('CANDIDAT')
            row.cell('NOM ET PRENOMS')
            row.cell('SEXE')
            row.cell('NE LE')
            row.cell('LIEU')

            if anonymat:
                row.cell('ANONYMAT')
            else:
                row.cell('ETAB. ORIGINE')

            if queryset:
                counter = 1
                for candidate in queryset:
                    row = table.row()
                    row.cell(f'{str(counter).zfill(2)}')
                    row.cell(str(candidate.table_num or ''))
                    row.cell(str(candidate.get_exam_type_display() or ''))
                    row.cell(str(candidate.student.get_full_name()))
                    row.cell(str(candidate.student.gender))
                    row.cell(f"{candidate.student.birth_date.strftime('%d/%m/%Y')}")
                    row.cell(str(candidate.student.birth_place).upper())
                    if anonymat:
                        row.cell(str(candidate.anonymat or ''))
                    else:
                        row.cell(str(candidate.school)[:12])
                    counter += 1


class CenterRoomsCandidatesListPDF(CenterCandidatesListPDF):
    def add_content(self, center, year=None, exam=''):
        for room in center.room_set.all():
            queryset = room.enrollment_set.select_related('student')\
                .order_by('student__last_name', 'student__first_name')
            super().add_content(queryset, center, year, exam, room=room)
        return self
        

class FicheTable(FPDF):
    def __init__(self, queryset=None, progress_recorder=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.add_font('TimesNewRoman', '', 'static/fonts/times.ttf', uni=True)
        self.add_font('TimesNewRoman', 'B', 'static/fonts/timesbd.ttf', uni=True)
        self.add_font('TimesNewRoman', 'BI', 'static/fonts/timesbi.ttf', uni=True)
        self.add_font('TimesNewRoman', 'I', 'static/fonts/timesi.ttf', uni=True)
        self.set_top_margin(5)
        self.add_page()
        
        self.year = None
        if queryset.exists():
            students = queryset.count()
        for i, candidate in enumerate(queryset):
            if not self.year:
                self.year = candidate.year.short_name
            self.add_candidate_data(candidate)

            if progress_recorder:
                percentage = int(i * 70 / students)
                progress_recorder.set_progress(20 + percentage, 100, f'Traitement: {candidate}...')

    
    def set_font(self, family, style= "", size = 0):
        if family.lower() == 'times':
            return super().set_font('TimesNewRoman', style, size)
        return super().set_font(family, style, size)
    
    def add_candidate_data(self, candidate):
        self.set_font('Times', 'BU', 12)
        self.cell(txt=f'FICHE DE TABLE CANDIDAT {candidate.get_exam_display()} {self.year} - DEC CHERIFLA', w=190, h=4, align='C')
        self.ln()
        self.add_info('Numéro de table', str(candidate.table_num))
        name_ar = ' | ' + reshape_text(str(candidate.student.get_full_name(constants.LANGUAGE_ARABIC)))
        if candidate.student.get_full_name(constants.LANGUAGE_ARABIC) == str(candidate):
            name_ar = ''
        self.add_info('Nom et Prénoms', f'{str(candidate)}{name_ar}', 'Sexe', str(candidate.student.get_gender_display()))
        self.add_info(
            'Date et lieu de naissance:', 
            f"{candidate.student.birth_date.strftime('%d/%m/%Y')} A {candidate.student.birth_place}")
        self.add_info('Centre', str(candidate.center), 'Salle', str(candidate.room.number))
        self.add_image(candidate.student.photo.url if candidate.student.photo else '')
        self.set_xy(self.x + 2, self.y + 5)
        self.cell(txt=str(candidate.get_exam_type_display()))
        self.line(5, self.y + 9, self.x + 20, self.y + 9)
        self.set_y(self.y + 12)

    
    def add_info(self, label, text, label2=None, text2=None):
        self.set_font('Times', 'I', 10)
        self.cell(txt=label)
        if label2:
            self.set_x(70)
            self.cell(txt=label2 + ':')
            self.set_font('Times', 'B', 14)
            self.set_x(self.x + 2)
            self.cell(txt=text2)
        self.set_font('Times', 'B', 14)
        self.ln(4)
        self.cell(txt=str(text))
        self.ln(8)

    def add_image(self, path=''):
        self.set_xy(170, self.y - 50)
        try:
            self.image(path, w=30, h=35)
        except:
            self.image('static/images/avatar.jpg', w=30, h=35)


class Convocation(BasePDF):
    def __init__(self, queryset, progress_recorder=None, *args, **kwargs):
        super().__init__(*args, **kwargs)

        if queryset.exists():
            first = queryset.first()
            students = queryset.count()
            exam_fr = first.get_exam_display().upper()
            exam_ar = custom_utils.get_exam_translation(exam_fr)
            center_name_ar = first.center.school.name_ar
            year = first.year
            for i, candidate in enumerate(queryset):
                self.add_candidate_data(candidate, year, exam_fr, exam_ar, center_name_ar)
                
                if progress_recorder:
                    percentage = int(i * 70 / students)
                    progress_recorder.set_progress(20 + percentage, 100, f'Traitement: {candidate}...')

    def footer(self):
        pass

    def add_candidate_data(self, candidate, year, exam_fr, exam_ar, center_name_ar=''):
        self.add_page()
        self.set_margins(0, 5, 0)
        self.set_font('Times', 'B', 12)
        self.set_x(self.x + 15)
        self.image('static/images/entete_portrait.jpg', w=180, h=45)
        self.ln()
        self.set_x(self.x + 10)
        self.line(self.x, self.y + 2, self.x + 190, self.y + 2)
        self.ln(6)
        self.set_x(self.x + 30)
        y = self.y

        self.set_font('Times', 'B', 12)
        txt = reshape_text('دعـــــــــــــــــــــــــــــــوة' + ' | ' + f'{exam_ar}' + f'{year.short_name}')
        self.multi_cell(w=150, h=5, txt= txt + f'\nCONVOCATION AUX EPREUVES DU {exam_fr} | SESSION {year.short_name}', align='C', border=1)

        self.set_font(size=11)

        self.set_xy(x=175, y=self.y + 15)
        try:
            self.image(f'{candidate.student.photo.url}', h=30, w=25)
        except:
            self.image(f'static/images/avatar.jpg', h=30, w=25)
        
        self.set_xy(x=10, y=self.y - 40)
        y = self.y
        text = f"Numéro de table:                                                  **{candidate.table_num}**\n" \
        f"L'élève: **{candidate}**\n" \
        f"à l'établissement: **{candidate.school}**\n" \
        f"Candidat(e) à l'examen du :      **{exam_fr}**\n" \
        f"\n\nEst invité(e) à se présenter au concours d'examen à l'établissement **--{candidate.center}--**  " \
        f"\n**--{candidate.room}--** pour y subir les épreuves qui s'y dérouleront le **{year.cepe_exam_date.strftime('%d/%m/%Y')}**\n" \
        "Heure (matin) :                                        **07h30**\n" \
        "Heure (soir) :                                           **14h00**"
        self.multi_cell(
            txt=text, 
            w=165, h=6 ,
            markdown=True,
        )
        full_name_ar = candidate.student.full_name_ar or ''
        self.set_xy(x=20, y=y)
        text = "رقم المقعد" + ":" +  "\n" \
        "السيّد" +  "(ة)" + ": " + "**" + f"{full_name_ar[:30]}" + "**" + "\n" \
        "في المدرسة" + ":\n" \
        "مرشح" + " (ة) " + "لامتحانات المرحلة" + " : " + f" {exam_ar} " "\n" \
        " يدعى للحضور في مركز الامتحانات بمدرسة" + "**" + f" {center_name_ar} " + "**" \
        " القاعة " + f" **{candidate.room}** \n" \
        "للمشاركة في الاختبارات التي ستجري فيها بتاريخ" + f" **{year.cepe_exam_date.strftime('%d/%m/%Y')}** \n\n\n" \
        "الوقت المعين" + "\n" \
        "الوقت المعين مساء" + "\n"
        self.multi_cell(
            txt=reshape_text(text), 
            w=145, h=6,
            markdown=True,
            align='R',
        )

        self.set_xy(x=10, y=self.y)
        self.line(self.x, self.y, self.x + 190, self.y)

        self.set_xy(x=10, y=self.y + 5)
        self.set_font('Times', 'B', 14)
        self.set_text_color(255, 0, 0)
        text = reshape_text("ملحوظة : يجب على المرشح {ة} الحضور بهذه الدعوة وبطاقة الطلاب ولن يسمع له بدخول قاعة") + "\n" + \
               reshape_text("الامتحانات بدونهما")
        self.multi_cell(w=190, txt=text, align='C')

        self.set_font('Times', size=12)
        self.set_text_color(0, 0, 0)
        self.ln()
        text = """
* واجبات المرشح {ة}
يجب على المرشح {ة} مراعاة ما يأتي والالتزام به.
1/ عرض الدعوة عند الباب قبل دخول مركز الامتحانات .
2/ وضع البطاقة الطلابية أمامه على الطاولة أثناء الامتحانات .
3/ عدم استعارة أي شي ء في قاعة الامتحانات، مثل {القلم - المسطرة - الورقة - وما شاكلها}
4/ عدم التأخر عن موعد الامتحانات.
5 / عدم الانشغال بأمر خارجي عن الامتحانات.
6/ عدم دخول مركز الامتحانات مع الجوالات.
7 / عدم الخروج من القاعة بدون إذن.
8/ احترام المسؤولين والمراقبين.
* حقوق المرشح {ة} = يحق للمرشح {ة}
1/ الاستفسار عن الكلمات الغامضة في الأسئلة . 2
2/ الاستفسار عند المراقب فقط.
3/ تقديم الشكاوى إلى لجنة الامتحانات عند الحاجة.
4 / الاستئذان من المراقب عند الضرورة.
5/ نيل الشهادة وحسن السيرة وكشف الدرجات عند احترام قوانين الامتحانات .
* الملحوظات :
1/ يمنع المتأخر عن الطور الأول المشاركة فيه .
2/ يمنع الغاش من المشاركة فيما غش فيه من المواد.
3/ يحرم المرشح {ة} من حسن السيرة والسلوك إذا أساء السلوك والأدب .
        
        """
        self.multi_cell(w=200, h=5, txt=reshape_text(text), align='R')
        self.set_xy(15, self.y - 50)
        self.image(qrcode.make(f"""DEC CHERIFLA
            CEPE SESSION {year.short_name}
            N° Table: {candidate.table_num}
            N° Inscription: {candidate.student.identifier}
            {candidate}""").get_image(), w=30, h=30)


class CenterCandidatesSignatureListPDF(BasePDF):
    def __init__(self, queryset, exam, year, room=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        first = queryset.first()
        center = first.center
        rooms = center.room_set.all()
        
        if room:
            room = models.Room.objects.filter(number=room, center=center).first()
            self.add_content(
                queryset.filter(room=room), 
                room=room, center=center, 
                year=year, exam=exam)
        else:
            for room in rooms:
                queryset = room.enrollment_set.select_related('student').order_by('student__last_name', 'student__first_name')
                self.add_content(
                    queryset=queryset, 
                    room=room, center=center, year=year, exam=exam)

    def add_content(self, queryset, room, center, exam=constants.EXAM_CEPE, year=None):
        self.add_page()
        self.set_margins(10, 10, 10)
        self.add_first_page_header()
        self.set_font('Times', 'B', 14)
        x = self.x + 15
        self.set_xy(x, self.y+5)
        exam = exam.upper()

        title = f'LISTE DES CANDIDATS AU {exam.upper()} - SESSION {year.short_name} '
        
        title += f"\nFICHE D'EMARGEMENT SALLE {room.number} / {center}".upper()

        self.multi_cell(w=263, h=8, txt=title, 
                border=1, align='C')
        self.set_xy(17, self.y + 5)
        boys = queryset.filter(student__gender=constants.GENDER_MALE).count()
        girls = queryset.filter(student__gender=constants.GENDER_FEMALE).count()
        students = boys + girls
        self.cell(txt=f'GARCONS : {str(boys).zfill(2)} |  ' + \
                  f'FILLES : {str(girls).zfill(2)} |  ' + \
                  f'TOTAL : {str(students).zfill(2)}' + \
                  f'{" " * 60}Date.................................................')
        self.set_y(self.y + 10)
        self.set_font('Times', '', 12)
        self.set_line_width(0.3)
        headings_style = FontFace(emphasis="BOLD", fill_color=(211, 211, 211))
        with self.table(
            # borders_layout="NO_HORIZONTAL_LINES",
            cell_fill_color=(224, 235, 255),
            col_widths=(10, 24, 80, 15, 30, 27, 27, 27, 27),
            headings_style=headings_style,
            line_height=9,
            text_align=('CENTER', "CENTER", 'LEFT', 'CENTER', 'CENTER', 'CENTER', 'CENTER', 'CENTER', 'CENTER'),
            width=260,
        ) as table:
            row = table.row()
            row.cell('N°')
            row.cell("N° TABLE")
            row.cell('NOM ET PRENOMS')
            row.cell('SEXE')
            row.cell('NE LE')
            row.cell('MATIERE 1 ...........')
            row.cell('MATIERE 2 ...........')
            row.cell('MATIERE 3 ...........')
            row.cell('MATIERE 4 ...........')

            if queryset:
                counter = 1
                for candidate in queryset.all():
                    row = table.row()
                    row.cell(f'{str(counter).zfill(2)}')
                    row.cell(str(candidate.table_num or ''))
                    row.cell(str(candidate.student.get_full_name()))
                    row.cell(str(candidate.student.gender))
                    row.cell(f"{candidate.student.birth_date.strftime('%d/%m/%Y')}")
                    row.cell('')
                    row.cell('')
                    row.cell('')
                    row.cell('')
                    counter += 1

        self.set_x(self.x + 10)
        self.add_page()
        self.cell(txt='**NOM, CONTACT ET SIGNATURE DES SURVEILLANTS**', h=5, markdown=True)
        self.line(20, self.y + 6, self.x + 150, self.y + 6)
        self.set_xy(20, self.y + 10)
        x, y = self.x, self.y
        self.multi_cell(txt=f"--**SURVEILLANT 1**--\nNom:\nContact:\nHeure:\nSignature", h=5, w=70, markdown=True)
        
        self.set_xy(x + 70, y)
        self.multi_cell(txt=f"--**SURVEILLANT 2**--\nNom:\nContact:\nHeure:\nSignature", h=5, w=70, markdown=True)
        
        self.set_xy(x + 70 * 2, y)
        self.multi_cell(txt=f"--**SURVEILLANT 3**--\nNom:\nContact:\nHeure:\nSignature", h=5, w=70, markdown=True)
        
        self.set_xy(x + 70 * 3, y)
        self.multi_cell(txt=f"--**SURVEILLANT 4**--\nNom:\nContact:\nHeure:\nSignature", h=5, w=70, markdown=True)

# Report de notes oral
class FicheOralePDF(BasePDF):
    def __init__(self, queryset, exam, year, room=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        first = queryset.first()
        center = first.center
        rooms = center.room_set.all()
        
        if room:
            room = models.Room.objects.filter(number=room, center=center).first()
            self.add_content(
                queryset.filter(room=room), 
                room=room, center=center, 
                year=year, exam=exam)
        else:
            for room in rooms:
                room_candidates = room.enrollment_set.select_related('student').order_by('student__last_name', 'student__first_name')
                self.add_content(
                    queryset=room_candidates, 
                    room=room, center=center, year=year, exam=exam)
        self.add_content(queryset, center, exam=exam, year=year)

    def add_content(self, queryset,  center, room=None, exam=constants.EXAM_CEPE, year=None):
        self.add_page()
        self.set_margins(10, 10, 10)
        self.add_first_page_header()
        self.set_font('Times', '', 14)
        x = self.x + 15
        self.set_xy(x, self.y+5)
        exam = exam.upper()
        title = f"EXAMEN DU {exam.upper()} SESSION {year.short_name}\n"
        room_text = f'{str(room).upper()} /' if room else ''
        title = f"LISTE {'GENERALE' if not room else ''} DE REPORT DES NOTES: **EPREUVE ORALE DU {exam.upper()} SESSION {year.short_name}** \n {room_text} {center}".upper()

        self.multi_cell(w=263, h=8, txt=title, 
                border=1, align='C', markdown=True)
        self.set_xy(17, self.y + 5)
        boys = queryset.filter(student__gender=constants.GENDER_MALE).count()
        girls = queryset.filter(student__gender=constants.GENDER_FEMALE).count()
        students = boys + girls
        self.cell(txt=f'GARCONS : {str(boys).zfill(2)} |  ' + \
                  f'FILLES : {str(girls).zfill(2)} |  ' + \
                  f'TOTAL : {str(students).zfill(2)}' + \
                  f'{" " * 60}Date.................................................')
        self.set_y(self.y + 10)
        self.set_font('Times', '', 12)
        self.set_line_width(0.3)
        headings_style = FontFace(emphasis="BOLD", fill_color=(211, 211, 211))
        with self.table(
            # borders_layout="NO_HORIZONTAL_LINES",
            cell_fill_color=(224, 235, 255),
            col_widths=(10, 35, 130, 30, 45, 90, 40),
            headings_style=headings_style,
            line_height=9,
            text_align=('CENTER', "CENTER", 'LEFT', 'CENTER', 'CENTER', 'CENTER', 'CENTER', 'CENTER', 'CENTER'),
            width=260,
        ) as table:
            row = table.row()
            row.cell('N°')
            row.cell("N° TABLE")
            row.cell('NOM ET PRENOMS')
            row.cell('SEXE')
            row.cell('NE LE')
            row.cell('LIEU')
            row.cell('NOTE')

            if queryset:
                counter = 1
                for candidate in queryset.all():
                    row = table.row()
                    row.cell(f'{str(counter).zfill(2)}')
                    row.cell(str(candidate.table_num or ''))
                    row.cell(str(candidate.student.get_full_name()))
                    row.cell(str(candidate.student.gender))
                    row.cell(f"{candidate.student.birth_date.strftime('%d/%m/%Y')}")
                    row.cell(str(candidate.student.birth_place))
                    row.cell('               /20')
                    counter += 1


# Report de notes écrites
class FicheReportEcrit(BasePDF):
    def __init__(self, queryset, exam, year, room=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        first = queryset.first()
        center = first.center
        rooms = center.room_set.all()
        subjects = models.Subject.objects.get_queryset(exam=exam)
        self.add_content(subjects, queryset, center, exam=exam, year=year)
        self.add_legend(subjects, year, exam)

    def add_content(self, subjects, queryset,  center, exam=constants.EXAM_CEPE, year=None):
        self.add_page()
        self.set_margins(10, 10, 10)
        self.add_first_page_header()
        self.set_font('Times', '', 14)
        x = self.x + 15
        self.set_xy(x, self.y+5)
        exam = exam.upper()
        title = f"EXAMEN DU {exam.upper()} SESSION {year.short_name}\n"
        title = f"LISTE DE REPORT DES NOTES: **EPREUVES ECRITES DU {exam.upper()} SESSION {year.short_name}** \n{center}".upper()

        self.multi_cell(w=263, h=8, txt=title, 
                border=1, align='C', markdown=True)
        self.set_xy(17, self.y + 5)
        boys = queryset.filter(student__gender=constants.GENDER_MALE).count()
        girls = queryset.filter(student__gender=constants.GENDER_FEMALE).count()
        students = boys + girls
        self.cell(txt=f'GARCONS : {str(boys).zfill(2)} |  ' + \
                  f'FILLES : {str(girls).zfill(2)} |  ' + \
                  f'TOTAL : {str(students).zfill(2)}' + \
                  f'{" " * 60}Date.................................................')
        self.set_y(self.y + 10)
        self.set_font('Times', '', 11)
        self.set_line_width(0.3)
        headings_style = FontFace(fill_color=(211, 211, 211))
        
        widths = (5, 8)
        aligns = ('CENTER', 'CENTER')

        for subject in subjects:
            widths += 5,
            aligns += 'CENTER',
        
        with self.table(
            # borders_layout="NO_HORIZONTAL_LINES",
            cell_fill_color=(224, 235, 255),
            col_widths=widths,
            headings_style=headings_style,
            line_height=9,
            text_align=aligns,
            width=275,
        ) as table:
            row = table.row()
            row.cell('N°')
            row.cell("N° TABLE")
            for subject in subjects:
                row.cell(f"{subject.order}\n{str(subject).upper()[:4]}")

            if queryset:
                counter = 1
                for candidate in queryset.all():
                    row = table.row()
                    row.cell(f'{str(counter).zfill(2)}')
                    row.cell(str(candidate.table_num or ''))
                    for subject in subjects:
                        row.cell('')
                    counter += 1
    
    def add_legend(self, subjects, year, exam):
        self.add_page()
        headings_style = FontFace(emphasis="BOLD", fill_color=(211, 211, 211))
        
        widths = (10, 80, 50)
        aligns = ('CENTER', 'LEFT', 'RIGHT')
        
        with self.table(
            # borders_layout="NO_HORIZONTAL_LINES",
            cell_fill_color=(224, 235, 255),
            col_widths=widths,
            headings_style=headings_style,
            line_height=8,
            text_align=aligns,
            width=150,
        ) as table:
            row = table.row()
            row.cell('N°')
            row.cell("MATIERE")
            row.cell("TRADUCTION")

            counter = 1
            for subject in subjects:
                row = table.row()
                row.cell(f'{str(counter).zfill(2)}')
                row.cell(str(subject).upper())
                row.cell(reshape_text(subject.translation))
                counter += 1


class FicheReportExamenBlanc(BasePDF):
    def __init__(self, queryset, exam, year, room=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        first = queryset.first()
        center = first.center
        self.school = first.school
        rooms = center.room_set.all()
        subjects = models.Subject.objects.get_queryset(exam=exam)
        self.add_content(subjects, queryset, center, exam=exam, year=year)
        self.add_legend(subjects, year, exam)

    def add_content(self, subjects, queryset,  center, exam=constants.EXAM_CEPE, year=None):
        self.add_page()
        self.set_margins(10, 10, 10)
        self.add_first_page_header()
        self.set_font('Times', '', 12)
        x = self.x + 15
        self.set_xy(x, self.y+5)
        exam = exam.upper()
        title = f"**EXAMEN BLANC DU {exam.upper()} SESSION {year.short_name}\n**"
        title += f"LISTE DE REPORT DES NOTES: {self.school}".upper()

        self.multi_cell(w=263, h=8, txt=title, 
                border=1, align='C', markdown=True)
        self.set_xy(17, self.y + 5)
        boys = queryset.filter(student__gender=constants.GENDER_MALE).count()
        girls = queryset.filter(student__gender=constants.GENDER_FEMALE).count()
        students = boys + girls
        self.cell(txt=f'GARCONS : {str(boys).zfill(2)} |  ' + \
                  f'FILLES : {str(girls).zfill(2)} |  ' + \
                  f'TOTAL : {str(students).zfill(2)}' + \
                  f'{" " * 60}Date.................................................')
        self.set_y(self.y + 10)
        self.set_font('Times', '', 11)
        self.set_line_width(0.3)
        headings_style = FontFace(fill_color=(211, 211, 211))
        
        widths = (5, 8, 20)
        aligns = ('CENTER', 'CENTER', 'LEFT')

        for subject in subjects:
            widths += 5,
            aligns += 'CENTER',
        
        with self.table(
            # borders_layout="NO_HORIZONTAL_LINES",
            cell_fill_color=(224, 235, 255),
            col_widths=widths,
            headings_style=headings_style,
            line_height=9,
            text_align=aligns,
            # width=275,
        ) as table:
            row = table.row()
            row.cell('N°')
            row.cell("N° TABLE")
            row.cell("NOM ET PRENOMS")
            for subject in subjects:
                row.cell(f"{subject.order}\n{str(subject).upper()[:3]}")

            if queryset:
                counter = 1
                for candidate in queryset.all():
                    row = table.row()
                    row.cell(f'{str(counter).zfill(2)}')
                    row.cell(str(candidate.table_num or ''))
                    row.cell(str(candidate.student.get_full_name()))
                    for subject in subjects:
                        row.cell('')
                    counter += 1
    
    def add_legend(self, subjects, year, exam):
        self.add_page()
        headings_style = FontFace(emphasis="BOLD", fill_color=(211, 211, 211))
        
        widths = (10, 80, 50)
        aligns = ('CENTER', 'LEFT', 'RIGHT')
        
        with self.table(
            # borders_layout="NO_HORIZONTAL_LINES",
            cell_fill_color=(224, 235, 255),
            col_widths=widths,
            headings_style=headings_style,
            line_height=8,
            text_align=aligns,
            width=150,
        ) as table:
            row = table.row()
            row.cell('N°')
            row.cell("MATIERE")
            row.cell("TRADUCTION")

            counter = 1
            for subject in subjects:
                row = table.row()
                row.cell(f'{str(counter).zfill(2)}')
                row.cell(str(subject).upper())
                row.cell(reshape_text(subject.translation))
                counter += 1


class ResultsStatistics(BasePDF):
    def add_content(self, queryset, user=None, for_location=False, summary=None):
        self.add_page()
        self.add_first_page_header()

        self.set_font('Times', 'B', 14)
        self.set_xy(self.x + 35, self.y+5)

        location = None
        year = None
        if queryset.exists():
            first = queryset.first()
            location = None
            year = custom_utils.get_current_year()

            if not for_location:
                location = str(first.location)
                year = str(first.year)
            else:
                location = str(first)
        title = ''
        label = ''
        if not for_location:
            label = 'CENTRE'
            title = f'STATISTIQUES DES CENTRES DE: {location.upper()} {year}'
        else:
            label = 'LOCALITE'
            title = f'STATISTIQUES NATIONALES PAR LOCALITE ({year})'

        if user and user.role == constants.ROLE_COMMISSION_NATIONALE:
            if not for_location:
                title = f'STATISTIQUES NATIONALES DES CENTRES CHERIFLA {year}'
        
        self.cell(w=225, h=8, txt=title, border=1, align='C')
        self.set_y(self.y + 15)
        self.set_font_size(12)

        TABLE_DATA = [[
            "N°", label, "G", "F", "T", "G", "F", "T", 
            "G", "F", "T", "G", "F", "T"
        ]]

        if not for_location:
            TABLE_DATA[0].append(reshape_text(constants.CENTER_TRANSLATION))
        else:
            TABLE_DATA[0].append('LOCALITE')

        for index, obj in enumerate(queryset):
            boys_perc = 0
            girls_perc = 0
            students_perc = 0
            if obj.boys_perc:
                boys_perc = f'{obj.boys_perc:.2f}'
            if obj.girls_perc:
                girls_perc = f'{obj.girls_perc:.2f}'
            if obj.students_perc:
                students_perc = f'{obj.students_perc:.2f}'

            data = [
            f'{str(index + 1).zfill(2)}',
            f'{obj}',
            f'{str(obj.boys_count).zfill(2)}',
            f'{str(obj.girls_count).zfill(2)}',
            f'{str(obj.students_count).zfill(2)}',
            f'{str(obj.boys_present).zfill(2)}',
            f'{str(obj.girls_present).zfill(2)}',
            f'{str(obj.students_present).zfill(2)}',
            f'{str(obj.boys_admitted).zfill(2)}',
            f'{str(obj.girls_admitted).zfill(2)}',
            f'{str(obj.students_admitted).zfill(2)}',
            f'{boys_perc}%',
            f'{girls_perc}%',
            f'{students_perc}%']
            
            if not for_location:
                data.append(f'{reshape_text(str(obj.school.name_ar)[:20])}')
            else:
                data.append(f'{obj}')

            TABLE_DATA.append(data)
        
        # General stats

        if for_location and summary:
            boys_perc = summary.get('boys_perc') or 0
            girls_perc = summary.get('girls_perc') or 0
            students_perc = summary.get('students_perc') or 0
            boys_perc = f'{boys_perc:.2f}'
            girls_perc = f'{girls_perc:.2f}'
            students_perc = f'{students_perc:.2f}'

            data = [
            f'{str(index + 1).zfill(2)}',
            f'TOTAL',
            f'{str(summary.get("boys_count")).zfill(2)}',
            f'{str(summary.get("girls_count")).zfill(2)}',
            f'{str(summary.get("students_count")).zfill(2)}',
            f'{str(summary.get("boys_present")).zfill(2)}',
            f'{str(summary.get("girls_present")).zfill(2)}',
            f'{str(summary.get("students_present")).zfill(2)}',
            f'{str(summary.get("boys_admitted")).zfill(2)}',
            f'{str(summary.get("girls_admitted")).zfill(2)}',
            f'{str(summary.get("students_admitted")).zfill(2)}',
            f'{boys_perc}%',
            f'{girls_perc}%',
            f'{students_perc}%', 
            f'TOTAL', 
            ]

            TABLE_DATA.append(data)
        # End


        self.set_x(87.6)
        self.cell(txt=f'CANDIDATS', border=1, w=31.1, align='C')
        self.cell(txt='PRESENTS', border=1, w=31.1, align='C')
        self.cell(txt='ADMIS', border=1, w=31.1, align='C')
        self.cell(txt='POURCENTAGE', border=1, w=54.2, align='C')

        self.set_y(self.y + 4.3)
        aligns = ('CENTER', 'LEFT', 'CENTER', 
                'CENTER', 'CENTER', 'CENTER', 
                'CENTER', 'CENTER', 'CENTER', 
                'CENTER', 'CENTER', 'CENTER', 
                'CENTER', 'CENTER', 'RIGHT')
        widths = (8, 52, 8, 8, 8, 8, 8, 8, 8, 8, 8, 14, 14, 14, 40)

        with self.table(
            line_height=8, cell_fill_mode="ROWS", 
            cell_fill_color=(210, 210, 210), 
            col_widths=widths, 
            text_align=aligns) as table:
            for data_row in TABLE_DATA:
                row = table.row()
                for datum in data_row:
                    row.cell(datum)


class ResultsStatisticsSummarized(BasePDF):
    def add_content(self, queryset, user=None, for_location=False, summary=None):
        self.add_page()
        self.add_first_page_header()

        self.set_font('Times', 'B', 14)
        self.set_xy(self.x + 35, self.y+5)

        location = None
        year = None
        if queryset.exists():
            first = queryset.first()
            location = str(first.location)
            year = str(first.year)
        title = f'STATISTIQUES DES CENTRES DE: {location.upper()} ({year})'

        if user and user.role == constants.ROLE_COMMISSION_NATIONALE:
            title = 'STATISTIQUES NATIONALES DES CENTRES CHERIFLA'

        self.cell(w=225, h=8, txt=title, border=1, align='C')
        self.set_y(self.y + 15)
        self.set_font_size(12)

        TABLE_DATA = (
            (
                "N°", "LOCALITE", 'CENTRE', 'CANDIDATS', 'PRESENTS', 'ADMIS', 'POURC.', 
                reshape_text(constants.CENTER_TRANSLATION)
            ),
        )

        for index, center in enumerate(queryset):
            students_perc = 0
            if center.students_perc:
                students_perc = f'{center.students_perc:.2f}'

            TABLE_DATA += (
                f'{str(index + 1).zfill(2)}',
                f'{str(center.location)}',
                f'{str(center)}',
                f'{str(center.students_count).zfill(2)}',
                f'{str(center.students_present).zfill(2)}',
                f'{str(center.students_admitted).zfill(2)}',
                f'{str(students_perc)}%',
                f'{reshape_text(str(center.school.name_ar))}',
            ),

        self.set_y(self.y + 5)
        widths = (8, 35, 70, 27, 25, 18, 20, 55)
        with self.table(
            line_height=8, cell_fill_mode="ROWS", 
            cell_fill_color=(210, 210, 210), 
            col_widths=widths, text_align=(
                'CENTER', 'LEFT', 'LEFT', 'CENTER', 
                'CENTER', 'CENTER', 'CENTER', 'RIGHT'),) as table:
            for data_row in TABLE_DATA:
                row = table.row()
                for datum in data_row:
                    row.cell(datum)


class CandidatesResults(BasePDF):
    def __init__(
            self, queryset, centers=None, user=None, 
            alpha_order=False, summarized=False, 
            french=True, for_location=False, *args, **kwargs):
        
        super().__init__(*args, **kwargs)
        first = queryset.first()
        exam = ''
        year = custom_utils.get_current_year()

        if queryset.exists():
            exam = first.exam

        center = None
        distinctions = models.Distinction.objects.values(
            'name', 'translation', 'average'
        )
        
        self.distinctions_french = {}
        self.distinctions_arabic = {}
        for distinction in distinctions:
            grade = str(int(distinction.get('average')))
            self.distinctions_french[grade] = distinction.get('name')
            self.distinctions_arabic[grade] = reshape_text(distinction.get('translation'))

        if first:
            year = first.year
            center = first.center
        
        subjects = models.Subject.objects.get_queryset(
            exam=exam).only('name', 'translation')

        method = self.add_content
        if summarized:
            method = self.add_content_summarized
        
        if not centers:
            method(subjects, queryset, exam, center, 
                   year, french=french, for_location=for_location)
        else:
            for center in centers:
                ordering = ('student__last_name', 'student__first_name',) if alpha_order else ('-gen_average',)
                queryset = models.Enrollment.candidates.get_candidates(
                    user=user, year=year
                ).filter(center__id=center.id).order_by(*ordering)
                method(subjects, queryset, exam=center.exam, 
                       center=center, year=center.year, french=french, 
                       for_location=for_location)
            
    def get_distinction(self, average, french):
        key = str(int(average))
        if french:
            return self.distinctions_french.get(key) or ''
        return self.distinctions_arabic.get(key) or ''
    
    def get_decision(self, year, exam, average, gender, french):
        return custom_utils.get_decision(year, exam, average, gender, french)

    def add_content(self, subjects, queryset, exam=None, 
        center=None, year=None, french=True, for_location=False):
        self.add_page()
        self.set_margins(10, 10, 10)
        self.add_first_page_header()
        self.set_font('Times', '', 14)
        x = self.x + 15
        self.set_xy(x, self.y+5)

        center_name = str(center) if french else center.school.name_ar        
        title = f"RESULTATS DES CANDIDATS AU {exam.upper()} SESSION {year.short_name}** " \
            f"\n{reshape_text(center_name) if not for_location else str(center.location)}".upper()

        self.multi_cell(w=263, h=8, txt=title, 
                border=1, align='C', markdown=True)
        self.set_xy(17, self.y + 5)
        
        self.set_y(self.y + 10)
        self.set_font('Times', '', 10)
        self.set_line_width(0.3)
        headings_style = FontFace(fill_color=(240, 240, 240))
        
        widths = [5, 8, 27]
        aligns = ['CENTER', 'CENTER', 'LEFT']

        no_grades = []
        for index in range(subjects.count()):
            widths += 5,
            aligns += 'CENTER',
            no_grades.append(0)
        
        aligns.append('CENTER')
        aligns.append('CENTER')
        widths.append(8)
        widths.append(8)

        headers = [
            "N°", "N° TABLE", "NOM ET PRENOMS"
        ]
        for subject in subjects:
            headers.append(str(subject).upper()[:3])
        headers.append('TOT')
        headers.append('MOY')

        if not french:
            headers = [
                reshape_text('رقم'), reshape_text('رقم طاولة'),
                reshape_text('إسم و لقب')
            ]   
            for subject in subjects.order_by('order'):
                headers.append(reshape_text(subject.translation)[:5].upper())
            headers.append(reshape_text('المجموع'))
            headers.append(reshape_text('المعدل'))
            headers.reverse()

        if not french:
            widths.reverse()
            aligns.reverse()
            aligns[-3] = 'RIGHT'
            
        with self.table(
            cell_fill_color=(230, 230, 230),
            col_widths=widths,
            headings_style=headings_style,
            line_height=9,
            text_align=aligns,
            width=275,
            cell_fill_mode="ROWS"
        ) as table:
            row = table.row()
            for header in headers:
                row.cell(header)

            if queryset:
                counter = 1
                for candidate in queryset.all():
                    row = table.row()
                    if french:
                        row.cell(f'{str(counter).zfill(2)}')
                        row.cell(str(candidate.table_num or ''))
                        row.cell(str(candidate))
                        grades = candidate.grade_set.only('value') or no_grades
                        exists = bool(grades)
                        for index, grade in enumerate(grades):
                            if isinstance(grades, list) or not exists:
                                grade = no_grades[0]
                            else:
                                grade = grade.value
                            row.cell(f'{str(grade).zfill(2)}')
                        row.cell(f'{candidate.total:.0f}')
                        row.cell(f'{candidate.gen_average:.2f}')
                    else:
                        row.cell(f'{candidate.gen_average:.2f}')
                        row.cell(f'{candidate.total:.0f}')
                        grades = candidate.grade_set.order_by('-subject__order').only('value') or no_grades
                        exists = bool(grades)
                        for index, grade in enumerate(grades):
                            if isinstance(grades, list) or not exists:
                                grade = no_grades[0]
                            else:
                                grade = grade.value
                            row.cell(f'{str(grade).zfill(2)}')
                        row.cell(reshape_text(candidate.student.full_name_ar))
                        row.cell(str(candidate.table_num or ''))
                        row.cell(f'{str(counter).zfill(2)}')
                    counter += 1

    def add_content_summarized(self, subjects, queryset, exam=None, 
                               center=None, year=None, french=False, 
                               for_location=False):
        self.add_page()
        self.set_margins(10, 10, 10)
        self.add_first_page_header()
        self.set_font('Times', '', 14)
        x = self.x + 15
        self.set_xy(x, self.y+5)
        
        center_name = str(center) if french else f'{constants.CENTER_TRANSLATION} : {center.school.name_ar}'
        title = f"RESULTATS DES CANDIDATS AU {exam.upper()} SESSION {year.short_name}**" + \
                f"\n{reshape_text(center_name) if not for_location else str(center.location)}".upper()

        self.multi_cell(w=263, h=8, txt=title, 
                border=1, align='C', markdown=True)
        self.set_xy(17, self.y + 5)
        
        self.set_y(self.y + 10)

        if french:
            self.set_font('Times', '', 9)
        else:
            self.set_font('Times', '', 10)
        self.set_line_width(0.3)
        headings_style = FontFace(fill_color=(240, 240, 240))
        
        headers = [
            'N°', 'N° TABLE', 'N° INSCRIPTION',
            'NOM ET PRENOMS', 'NE LE', "ECOLE D'ORIGINE", 'POINTS', 'MOY.', 'E. BLANC',
            'MOY. GEN', 'MENTION', 'DECISION'
        ]
        if not french:
            headers = [
                reshape_text('رقم'), reshape_text('رقم طاولة'), 
                reshape_text('رقم التسجيل'), reshape_text('إسم و لقب'),
                reshape_text('المولود (ه)'), reshape_text('مدرسة'), reshape_text('المجموع'),
                reshape_text('الامتحان'), reshape_text('الاختبار التجريبي'),
                reshape_text('المعدل العام'), reshape_text('التقدير'), 
                reshape_text('نتيجة') 
            ]
        widths = [8, 15, 22, 40, 15, 35, 12, 15, 15, 15, 20, 15]
        aligns = [
            'CENTER', 'CENTER', 'CENTER', 'LEFT', 'CENTER', 
            'CENTER', 'CENTER', 'CENTER', 'CENTER', 'CENTER',
            'CENTER', 'CENTER'
        ]
        
        if not french:
            widths.reverse()
            # widths[-3] += + 5
            # widths[4] -= 10
            # widths[5] += 3

            aligns.reverse()
            aligns[-4] = 'RIGHT'
            headers.reverse()

        with self.table(
            cell_fill_color=(230, 230, 230),
            col_widths=widths,
            headings_style=headings_style,
            line_height=9,
            text_align=aligns,
            width=275,
            cell_fill_mode="ROWS"
        ) as table:
            row = table.row()
            for i, header in enumerate(headers):
                if i == len(headers) - 3:
                    self.set_text_color(0, 0, 255)
                row.cell(header)
                self.set_text_color(0, 0, 0)
                
            if queryset.exists():
                counter = 1
                for i, candidate in enumerate(queryset.all()):
                    decision = self.get_decision(year, exam, 
                        candidate.gen_average, candidate.student.gender, french)
                    row = table.row()
                    if french:
                        row.cell(f'{str(counter).zfill(2)}')
                        row.cell(str(candidate.table_num or ''))
                        row.cell(str(candidate.student.identifier or ''))
                        row.cell(f'{str(candidate)}')
                        row.cell(f"{candidate.student.birth_date.strftime('%d/%m/%Y')}")
                        row.cell(f"{str(candidate.school)[:15]}")
                        row.cell(f'{candidate.total:.0f}')
                        row.cell(f'{candidate.average:.2f}')
                        if candidate.mock_average:
                            row.cell(f'{candidate.mock_average:.2f}')
                        else:
                            row.cell('-')
                        self.set_text_color(0, 0, 255)
                        row.cell(f'{candidate.gen_average:.2f}')
                        
                        self.set_text_color(0, 0, 0)
                        row.cell(f'{self.get_distinction(candidate.gen_average, True)}')
                        row.cell(f'{decision}')
                    else:
                        row.cell(f'{reshape_text(decision)}')
                        row.cell(f'{self.get_distinction(candidate.gen_average, False)}')
                        self.set_text_color(0, 0, 0)
                        row.cell(f'{candidate.gen_average:.2f}')
                        self.set_text_color(0, 0, 255)
                        if candidate.mock_average:
                            row.cell(f'{candidate.mock_average:.2f}')
                        else:
                            row.cell('-')
                        row.cell(f'{candidate.average:.2f}')
                        row.cell(f'{candidate.total:.0f}')
                        row.cell(f'{reshape_text(candidate.school.name_ar)}')
                        row.cell(f"{candidate.student.birth_date.strftime('%d/%m/%Y')}")
                        row.cell(reshape_text(candidate.student.full_name_ar))
                        row.cell(str(candidate.student.identifier or ''))
                        row.cell(str(candidate.table_num or ''))
                        row.cell(f'{str(counter).zfill(2)}')
                    counter += 1
                    self.set_text_color(0, 0, 0)
                


class Bulletin(BasePDF):
    def __init__(self, queryset, progress_recorder=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.exam = queryset.first().exam
        self.year = queryset.first().year
        self.year_date = self.year.report_date

        self.set_font('Arial', '', size=10)
        self.green_color = 0, 150, 0
        qs = models.Subject.objects.get_queryset(
            self.exam).order_by('order')
        self.subjects_fr = [s.name for s in qs]
        self.subjects_ar = [s.translation for s in qs]
        self.distinctions_french = {}
        self.distinctions_arabic = {}
        for distinction in models.Distinction.objects.all():
            grade = str(int(distinction.average))
            self.distinctions_french[grade] = distinction.name
            self.distinctions_arabic[grade] = distinction.translation

        if queryset.exists():
            students_count = queryset.count()
            self.correction_center = str(queryset.first().center.correction_center.correction_center).upper()
            self.correction_center_ar = str(queryset.first().center.correction_center.correction_center.translation)
            for i, candidate in enumerate(queryset):
                self.add_content(candidate, self.year, self.exam)

                if progress_recorder:
                    percentage = int(i * 60 / students_count)
                    progress_recorder.set_progress(30 + percentage, 100, f'Traitement: {candidate}...')

    def footer(self):
        pass

    def header(self):
        # Left side
        self.set_margins(10, 10, 10)
        self.multi_cell(w=65,
            txt="Ministère de l'Education Nationale"
            " et de l'Alphabétisation\n"
            "Plate-forme des structures islamiques\n"
            "d'éducation (SIE)", align='C', h=4)
        
        y = self.y + 5
        self.image('static/images/sie.jpg', x=20, y=y, h=15, w=15)
        self.image('static/images/logo.jpg', x=38, y=y, h=15, w=15)

        # Middle
        self.set_font('Times', 'B', 12)
        self.set_text_color(self.green_color)
        self.set_xy(82, 5)
        basmallah = "بسم الله الرحمان الرحيم"
        salim = "وصلى الله على الرسول الكريم وسلم"
        self.multi_cell(txt=reshape_text(basmallah + '\n' + salim), w=55, align='C')

        self.set_xy(105, 10)
        self.set_font('Arial', '', 10)
        self.set_text_color(0, 0, 0)
        self.multi_cell(w=140, align='C', 
                        txt="République de Côte d'Ivoire\n" +
                        "Union - Discipline - Travail", h=4)
        self.image('static/images/armoiries.png', x=170, y=20, w=15, h=15)

        self.set_font('Arial', 'B', 10)
        self.set_xy(50, 30)
        txt = 'DIRECTION DES ECOLES CONFESSIONNELLES CHERIFLA\n'
        txt += '\nCOMMISSION NATIONALE DES EXAMENS ET CONCOURS\n'
        txt += 'TEL :   (+225) 0708150229 / 0505304662\n'
        txt += 'E-mail : <EMAIL>\n'
        txt += f'Année scolaire : {self.year}'
        self.multi_cell(txt=txt, w=120, h=5, align='C')
        
        self.set_xy(50, 35)
        self.set_font('Times', 'B', 12)
        self.cell(w=120, align='C', txt=reshape_text('والمسابقات الوطنية') + ' ' + reshape_text(' لجنة الإمتحانات'))
        
        self.line(10, self.y + 30, 200, self.y + 30)

        self.set_font('Times', 'B', 20)
        self.set_text_color(self.green_color)
        self.set_xy(50, 67)

        if self.exam == constants.EXAM_BEPC:
            self.cell(txt=f' {self.year.short_name} ' + reshape_text('كشف الدرجات الإعدادية'), w=100, align='C')
        else:
            self.cell(txt=f' {self.year.short_name} ' + reshape_text('كشف الدرجات للابتدائية'), w=100, align='C')

        self.set_xy(50, 75)
        self.set_font('Times', 'BU', 16)
        self.set_text_color(self.green_color)
        self.cell(txt=f'RELEVE DE NOTES {str(self.exam).upper()} ISLAMIQUE {self.year.short_name}', w=100, align='C')

    def add_content(self, candidate, year, exam):
        self.add_page()
        self.set_text_color(0, 0, 0)
        
        try:
            self.image(candidate.student.photo.url, w=22, h=25, x=170, y=68)
        except:
            self.image('static/images/avatar.jpg', w=22, h=25, x=170, y=68)
    
        self.set_xy(10, 95)
        self.set_font('Times', '', 11)
        self.set_text_color(0, 0, 0)
        txt = f"Numéro d'inscription : **{candidate.student.identifier}**\n"
        txt += f"Numéro de table : **{candidate.table_num}**\n"
        txt += f"Nom et Prénoms : **{str(candidate).upper()}**\n"
        txt += f"Né(e) le : **{candidate.student.birth_date.strftime('%d/%m/%Y')} à { str(candidate.student.birth_place).upper()}**\n"
        txt += f"Centre de composition : **{candidate.center}**\n"
        self.multi_cell(w=110, txt=txt, align='L', markdown=True, h=5)
        
        self.set_xy(115, 95)
        txt = reshape_text(f"**{candidate.student.identifier}**") + " : " + reshape_text('رقم التسجيل') + "\n"
        txt += reshape_text(f"**{candidate.table_num}**") + " : " + reshape_text('رقم طاولة') + "\n"
        txt += reshape_text(f"**{candidate.student.full_name_ar}**") + " : " + reshape_text('إسم و لقب') + "\n"
        txt += reshape_text(f"**{candidate.student.birth_date.strftime('%d/%m/%Y')} { str(candidate.student.birth_place).upper()}**") + " : " + reshape_text('المولود (ه)') + "\n"
        txt += reshape_text(f"**{candidate.center.school.name_ar}**") + " : " + reshape_text('مراكز الامتحانات') + "\n"
        self.multi_cell(w=85, txt=txt, align='R', markdown=True, h=5)

        self.line(10, self.y, 200, self.y)

        headers = [
            'Matières', 'obtenues', reshape_text('الكبرى'),
            reshape_text('المكتسبة'), reshape_text('المواد الدراسية')
        ]

        grades = [grade.value for grade in candidate.grade_set.order_by('subject__order')]
        if not candidate.grade_set.exists():
            grades = [
                0, 0, 0, 0, 0,
                0, 0, 0, 0, 0,
                0, 0, 0, 0, 0,
                0
            ]


        subtotal1 = sum(grades[:8])
        subtotal2 = sum(grades[8:])
        data = [
            [self.subjects_fr[0], str(grades[0]).zfill(2), 20, str(grades[0]).zfill(2), reshape_text(self.subjects_ar[0])],
            [self.subjects_fr[1], str(grades[1]).zfill(2), 20, str(grades[1]).zfill(2), reshape_text(self.subjects_ar[1])],
            [self.subjects_fr[2], str(grades[2]).zfill(2), 20, str(grades[2]).zfill(2), reshape_text(self.subjects_ar[2])],
            [self.subjects_fr[3], str(grades[3]).zfill(2), 20, str(grades[3]).zfill(2), reshape_text(self.subjects_ar[3])],
            [self.subjects_fr[4], str(grades[4]).zfill(2), 20, str(grades[4]).zfill(2), reshape_text(self.subjects_ar[4])],
            [self.subjects_fr[5], str(grades[5]).zfill(2), 20, str(grades[5]).zfill(2), reshape_text(self.subjects_ar[5])],
            [self.subjects_fr[6], str(grades[6]).zfill(2), 20, str(grades[6]).zfill(2), reshape_text(self.subjects_ar[6])],
            [self.subjects_fr[7], str(grades[7]).zfill(2), 20, str(grades[7]).zfill(2), reshape_text(self.subjects_ar[7])],
            ['TOTAL', subtotal1, 160, subtotal1, reshape_text('المجموع')],
            [self.subjects_fr[8], str(grades[8]).zfill(2), 20, str(grades[8]).zfill(2), reshape_text(self.subjects_ar[8])],
            [self.subjects_fr[9], str(grades[9]).zfill(2), 20, str(grades[9]).zfill(2), reshape_text(self.subjects_ar[9])],
            [self.subjects_fr[10], str(grades[10]).zfill(2), 20, str(grades[10]).zfill(2), reshape_text(self.subjects_ar[10])],
            [self.subjects_fr[11], str(grades[11]).zfill(2), 20, str(grades[11]).zfill(2), reshape_text(self.subjects_ar[11])],
            [self.subjects_fr[12], str(grades[12]).zfill(2), 20, str(grades[12]).zfill(2), reshape_text(self.subjects_ar[12])],
            [self.subjects_fr[13], str(grades[13]).zfill(2), 20, str(grades[13]).zfill(2), reshape_text(self.subjects_ar[13])],
            [self.subjects_fr[14], str(grades[14]).zfill(2), 20, str(grades[14]).zfill(2), reshape_text(self.subjects_ar[14])],
            [self.subjects_fr[15], str(grades[15]).zfill(2), 20, str(grades[15]).zfill(2), reshape_text(self.subjects_ar[15])],
            ['TOTAL', subtotal2, 160, subtotal2, reshape_text('المجموع')],
            ['TOTAL GENERAL', str(sum(grades)).zfill(2), 320, str(sum(grades)).zfill(2), reshape_text('مجموع الدرجات') + '\n' + reshape_text('المكتسبة')],
        ]
        aligns = ['LEFT', 'CENTER', 'CENTER', 'CENTER', 'RIGHT']
        widths = [54, 18, 15, 15, 25]

        self.set_y(self.y + 5)
        self.set_font('Times', '', 11)
        total = 0

        F = FontFace('Times', size_pt=11, emphasis='B', fill_color=(255, 255, 224))
        T = FontFace('Times', size_pt=11, emphasis='B', fill_color=(255, 213, 128))
        with self.table(
                text_align=aligns, cell_fill_mode='ROWS', 
                cell_fill_color=(220, 220, 220), 
                col_widths=widths, 
                width=120, line_height=6) as table:
            row = table.row()
            for header in headers:
                row.cell(header)

            for datum in data:
                row = table.row()
                is_total_row = str(datum[0]).lower() == 'total'
                for col in datum:
                    if is_total_row:
                        row.cell(str(col), style=F)
                    elif str(datum[0]).lower() == 'total general':
                        row.cell(str(col), style=T)
                    else:
                        row.cell(str(col))
        self.set_xy(8, 130)
        self.set_font('Times', 'B', 9)
        self.multi_cell(txt=reshape_text('ملاحظات') + '\nRésultats', w=36, border=1, align='C', h=3)

        self.set_x(8)
        average = candidate.gen_average or 'NC'
        if average and average != 'NC':
            average = f'{average:.2f}'
        
        if candidate.mock_average:
            self.multi_cell(txt=f"Examen Blanc: {round(candidate.mock_average or 0, 2)} /20\nExamen National: \n{round(candidate.average or 0, 2)} /20\nMoyenne Générale:\n{average}\n", w=36, border=1, align='C', h=4.8)
        else:
            self.multi_cell(txt=f"Examen Blanc: **NC**\nExamen National: \n{round(candidate.average or 0, 2)} /20\nMoyenne Générale:\n{average}\n", w=36, border=1, align='C', h=4.8)

        decision_fr = self.get_decision(year, exam, candidate.gen_average, candidate.student.gender, True)
        decision_ar = reshape_text(self.get_decision(year, exam, candidate.gen_average, candidate.student.gender, False))
        self.set_xy(8, self.y - 4.8)
        self.multi_cell(txt="\n" + reshape_text('معدل') + f'\nRésultat\n{decision_fr}\n' + \
                        decision_ar, w=36, border=1, align='C', h=4.8)
        
        distinction_fr = self.get_distinction(candidate.gen_average, True)
        distinction_ar = self.get_distinction(candidate.gen_average, False)
        self.set_xy(8, self.y)
        self.multi_cell(txt="\n" + reshape_text('التقدير') + f'\nAppréciation\n{distinction_fr}\n' + \
            reshape_text(distinction_ar), w=36, border=1, align='C', h=4.8)
        
        font_size = self.font_size
        self.set_font_size(12)
        self.set_xy(8, self.y)
        self.multi_cell(txt="\n" + reshape_text('رئيس لجنة الامتحانات') + '\nPrésident du Jury\n\n\n\n\n \n\n\n\n ', w=36, border=1, align='C', h=4)

        self.set_xy(50, 262)
        self.cell(txt=f'Fait à {self.correction_center} le :               {self.year_date}')

        self.set_xy(140, 262)
        self.cell(txt=reshape_text(f'حرر في {self.correction_center_ar} بتاريخ') + ' : ', align='R')

        # self.rotate(45)
        self.set_font_size(font_size)
        self.set_xy(165, 130) 
        self.multi_cell(w=35, txt=' \n ', border=1, h=30)
        
        self.set_xy(165, 176)
        self.set_font_size(14)
        self.rotate(90)
        self.multi_cell(w=35, txt='\n\n\n' + reshape_text('المواد الدينية') + ' \n RELIGION', h=4, align='C')
        
        self.rotate(0)
        self.set_xy(165, 185)
        self.multi_cell(w=35, txt=' \n ', border=1, h=30)
        
        self.set_xy(165, 235)
        self.rotate(90)
        self.multi_cell(w=35, txt='\n\n\n' + reshape_text('المواد العربية') + ' \n ARABE', h=4, align='C')

        self.set_font_size(12)
        self.rotate(0)
        # self.set_xy(100, 270)
        qr_text = 'DEC CHERIFLA\n'
        qr_text += f'RELEVE DE NOTES CEPE {year}\n'
        qr_text += f'N° TABLE CANDIDAT: {candidate.table_num}'
        self.image(qrcode.make(qr_text).get_image(), 95, 267, 20, 20)

    def get_decision(self, year, exam, average, gender, french=True):
        return custom_utils.get_decision(year, exam, average, gender, french)
    
    def get_distinction(self, average, french=True):
        key = str(int(average))
        if french:
            return self.distinctions_french.get(key, ' ') 
        return self.distinctions_arabic.get(key, ' ')


class AttestationBonneConduite(BasePDF):
    def __init__(self, queryset, progress_recorder=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.set_font('Arial', '', size=10)
        self.green_color = 0, 150, 0
        self.year = ''
        self.exam = constants.EXAM_CEPE
        if queryset.exists():
            self.year = str(queryset.first().year)
            self.year_short_name = str(queryset.first().year.short_name)
            self.exam = str(queryset.first().exam)
            self.correction_center = str(queryset.first().center.correction_center.correction_center).upper()
            self.correction_center_ar = str(queryset.first().center.correction_center.correction_center.translation)
            
            total_objs = queryset.count()
            for i, obj in enumerate(queryset):
                self.add_page()
                self.add_content(obj)
                if progress_recorder:
                    percentage = int(i * 60 / total_objs)
                    progress_recorder.set_progress(30 + percentage, 100, f'Traitement: {obj}...')

    def header(self):
        self.image('static/images/attestation_border.jpg', 0, 0, 210, 295)
        # Left side
        self.set_margins(10, 10, 10)
        self.set_xy(self.x + 5, self.y + 7)
        self.multi_cell(w=65,
            txt="Ministère de l'Education Nationale"
            " et de l'Alphabétisation\n"
            "Plate-forme des Structures Islamiques\n"
            "d'Education (SIE)", align='C', h=4)
        
        y = self.y + 5
        self.image('static/images/sie.jpg', x=20, y=y, h=15, w=15)
        self.image('static/images/logo.jpg', x=38, y=y, h=15, w=15)

        # Middle
        self.set_font('Times', 'B', 12)
        self.set_text_color(self.green_color)
        self.set_xy(82, 15)
        basmallah = "بسم الله الرحمان الرحيم"
        salim = "وصلى الله على الرسول الكريم وسلم"
        self.multi_cell(txt=reshape_text(basmallah + '\n' + salim), w=55, align='C')

        self.set_xy(100, 17)
        self.set_font('Arial', '', 10)
        self.set_text_color(0, 0, 0)
        self.multi_cell(w=140, align='C', 
                        txt="République de Côte d'Ivoire\n" +
                        "Union - Discipline - Travail", h=4)
        self.image('static/images/armoiries.png', x=170, y=25, w=15, h=15)

        self.set_font('Arial', 'B', 10)
        self.set_xy(50, 37)
        txt = 'DIRECTION DES ECOLES CONFESSIONNELLES CHERIFLA\n'
        txt += '\nCOMMISSION NATIONALE DES EXAMENS ET CONCOURS\n'
        txt += 'TEL :   (+225) 0708150229 / 0505304662\n'
        txt += 'E-mail : <EMAIL>\n'
        txt += f'Année scolaire : {self.year}'
        self.multi_cell(txt=txt, w=120, h=5, align='C')
        
        self.set_xy(50, 43)
        self.set_font('Times', 'B', 12)
        exam = custom_utils.get_exam_translation(self.exam)
        self.cell(w=120, align='C', txt=reshape_text('والمسابقات الوطنية') + ' ' + reshape_text(' لجنة الإمتحانات'))
        
        self.set_draw_color(200, 200, 200)

        self.set_font('Times', 'B', 20)
        self.set_text_color(self.green_color)
        self.set_xy(50, 72)
        self.cell(txt=f'(--{self.year_short_name} ' + reshape_text(f'{exam} ') + reshape_text(') ') + reshape_text('شهادة حسن السيرة') + '--', w=100, align='C', markdown=True)
        self.set_draw_color(0, 0, 0)

    def add_content(self, enrollment):
        birth_date = f'{enrollment.student.birth_date.strftime("%d/%m/%Y")}'
        self.set_font('Times', '', 14)
        self.set_y(self.y + 15)
        txt = reshape_text(' تشهد إدارة المدارس النموذجية شريفلا فـي كوت ديفوار')
        txt += "\n" + "**" + reshape_text(enrollment.student.full_name_ar or ' ') + "**" + \
            ' : ' + reshape_text(' بأنّ الطالب(ة)')
        txt += "\n"  + f' **{birth_date}** ' + ' : ' + reshape_text('              بتأريخ') \
            + "**" + reshape_text(str(enrollment.student.birth_place_ar).upper() or ' ') + "**" + ' : ' + reshape_text(' المولود( ة) في') 
        year = enrollment.year.translation or str(enrollment.year)
        txt += reshape_text(f"""
قد أتم (ت) دراسة المرحلة الابتدائية واجتاز(ت) امتحانها بنجاح باهر في العام الدراسي {year}   
و كذلك في جميع الاختبارات التي نظمها إدارة المدارس النموذجية شريفلا 
و كان(ت) مثالا لأخلاق و السيرة الحسنة طيلة دراسته (ها).
وبناء على طلبه (ها) منحت له (ها) هذه الشّهادة للاستفادة لدى الجهات المعينة.
        """)
        self.set_x(self.x + 10)
        self.multi_cell(w=170, h=8, txt=txt, align='R', markdown=True)
        
        self.set_xy(20, self.y + 10)
        self.line(20, self.y - 2 , 190, self.y - 2)
        
        self.set_y(self.y + 5)
        self.set_font('Arial', 'B', 16)
        self.set_text_color(self.green_color)
        extra = f'({enrollment.get_exam_display().upper()} {enrollment.year.short_name})'
        self.cell(w=185, align='C', txt=f'--ATTESTATION DE BONNE CONDUITE {extra})--', markdown=True)
        
        self.ln()
        self.set_xy(self.x + 10, self.y + 5)
        self.set_text_color(0, 0, 0)
        self.set_font('Times', '', 12)
        txt = f"""
La Direction des Ecoles Confessionnelles Chérifla de Côte d’Ivoire  
Atteste que  l’élève : **{ enrollment }**
Né (e) le  **{birth_date}**  à  **{enrollment.student.birth_place or ' '}**
N° du registre : **{ enrollment.student.identifier }**
A suivi régulièrement  et assidument les cours primaires élémentaires 
Et  s'est  illustré par un comportement exemplaire et une bonne conduite durant ses études à la dite école.
En foi de quoi, cette attestation lui est délivrée pour servir et valoir ce que de droit. 
        """
        self.multi_cell(w=170, h=6, txt=txt, align='L', markdown=True)

        self.ln(4)
        self.set_x(self.x + 10)
        self.cell(txt="**" + reshape_text(f'Fait à {self.correction_center} le') + ' ' * 25 + f'{enrollment.year.report_date} ' + "**", markdown=True)
        
        self.set_x(self.x + 10)
        self.cell(w=80, txt='**' + reshape_text('توقيع مدير الامتحانات و المسابقات') + '**', align='R', markdown=True)

    def footer(self):
        pass


class Diplome(BasePDF):
    def __init__(self, queryset, progress_recorder=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.green_color = 0, 150, 0
        self.brown_color = 121, 68, 59
        self.year = '2023-2024'
        self.year_ar = ''
        self.center = ''
        self.exam = constants.EXAM_CEPE
        self.report_date = ''
        self.center_ar = ''
        if queryset.exists():
            self.year = get_current_year()
            self.year_ar = self.year.translation or ' '
            self.center = str(queryset.first().center)
            self.center_ar = queryset.first().center.school.name_ar
            self.exam = queryset.first().get_exam_display()
            self.report_date = self.year.report_date
        distinctions = models.Distinction.objects.values(
            'name', 'translation', 'average'
        )
        self.correction_center = str(queryset.first().center.correction_center.correction_center).upper()
        self.correction_center_ar = str(queryset.first().center.correction_center.correction_center.translation)
        self.distinctions_french = {}
        self.distinctions_arabic = {}
        for distinction in distinctions:
            grade = str(int(distinction.get('average')))
            self.distinctions_french[grade] = distinction.get('name')
            self.distinctions_arabic[grade] = distinction.get('translation')
        
        total_objs = queryset.count()
        for i, obj in enumerate(queryset):
            self.add_page()
            self.add_content(obj)

            if progress_recorder:
                percentage = int(i * 60 / total_objs)
                progress_recorder.set_progress(30 + percentage, 100, f'Traitement: {obj}...')


    def header(self):
        self.set_font('Arial', '', 10)
        border_path = f'static/images/diplome_border_{self.year.short_name}.jpg'
        if not os.path.exists(border_path):
            border_path = 'static/images/diplome_border.jpg'
        self.image(border_path, 0, 0, 297, 210)

        # Left side
        self.set_margins(10, 10, 10)
        self.set_xy(self.x + 25, self.y + 25)
        self.multi_cell(w=65,
            txt="Ministère de l'Education Nationale"
            " et de l'Alphabétisation\n"
            "Plate-forme des Structures Islamiques\n"
            "d'éducation (SIE)", align='C', h=4)
        
        y = self.y + 5
        self.image('static/images/sie.jpg', x=40, y=y, h=15, w=15)
        self.image('static/images/logo.jpg', x=58, y=y, h=15, w=15)

        # Middle
        self.set_font('Times', 'B', 12)
        self.set_text_color(self.green_color)
        self.set_xy(130, 25)
        basmallah = "بسم الله الرحمان الرحيم"
        salim = "وصلى الله على الرسول الكريم وسلم"
        self.multi_cell(txt=reshape_text(basmallah + '\n' + salim), w=55, align='C')

        self.set_xy(175, 35.5)
        self.set_font('Arial', '', 10)
        self.set_text_color(0, 0, 0)
        self.multi_cell(w=130, align='C', 
                        txt="République de Côte d'Ivoire\n" +
                        "Union - Discipline - Travail", h=4)
        self.image('static/images/armoiries.png', x=235, y=45, w=15, h=15)

        self.set_font('Arial', 'B', 10)
        self.set_xy(100, 35)
        txt = 'DIRECTION DES ECOLES CONFESSIONNELLES CHERIFLA\n'
        txt += '\nCOMMISSION NATIONALE DES EXAMENS ET CONCOURS\n'
        txt += 'TEL :   (+225) 0708150229 / 0505304662\n'
        txt += 'E-mail : <EMAIL>\n'
        txt += f'Année scolaire : {self.year}'
        self.multi_cell(txt=txt, w=120, h=5, align='C')
        
        self.set_xy(90, 40)
        self.set_font('Times', 'B', 12)
        self.cell(w=120, align='C', txt=reshape_text('والمسابقات الوطنية') + ' ' + reshape_text(' لجنة الإمتحانات'))
        
        self.set_draw_color(200, 200, 200)

        self.set_font('Times', 'B', 20)
        self.set_text_color(self.brown_color)
        self.set_xy(57, 68)
        exam_translation = custom_utils.get_exam_translation(self.exam.lower())
        self.cell(txt=f'--{self.year.short_name} ' +  reshape_text('لِلدِّرَاسَاتِ الإسلامية') + ' ' + reshape_text(exam_translation) + '--', w=180, align='C', markdown=True)
        
        self.ln()
        self.set_font('Arial', '', 18)   
        self.ln()
        self.set_x(25)
        exam_fullname = constants.EXAMS_DEFINITIONS[self.exam.lower()]
        self.cell(txt=f"**--{exam_fullname} ISLAMIQUE {self.year.short_name}--**", markdown=True, w=240, align='C')
        
    def add_content(self, enrollment):
        # French
        birth_date = f'{enrollment.student.birth_date.strftime("%d/%m/%Y")}'
        distinction = self.get_distinction(enrollment.gen_average, True)
        distinction_ar = self.get_distinction(enrollment.gen_average, False)
        year = reshape_text(enrollment.year.translation or ' ')
        y  = self.y
        self.set_text_color(0, 0, 0)
        self.set_font('Times', '', 12)
        self.set_x(20)
        txt = \
        f"""

        LA DIRECTION DES ECOLES CONFESSIONNELLES 
        CHERIFLA DE CÔTE D'IVOIRE

        Vu le règlement en vigueur,
        Vu le rapport de la commission centrale des examens
        Siégeant à Abidjan pour la session **{self.year}**,
        Vu le procès-verbal d'examens établi par le jury
        Du centre **{self.center}**,
        Atteste que **{enrollment}**
        Né (e) le **{birth_date}** à  **{str(enrollment.student.birth_place).upper()}**
        A été jugé apte à obtenir avec la mention  **{distinction}**
        Pour jouir des droits et prérogatives qui y sont attachés

        """
        self.multi_cell(txt=txt, w=120, markdown=True, h=5)
        
        # Arabic
        self.set_font('Times', '', 16)
        self.set_xy(120, y)
        exam_translation = custom_utils.get_exam_translation(self.exam.lower())
        txt = \
        reshape_text(
        f"""
        
إنّ إدارة المدارس النموذجية شريفلا في كوت ديفوار 
نظرا للأنظمة السّارية 
نظرا لتقرير اللجنة المركزية للإمتحانات في أبيدجان لدورة **{self.year_ar}**    
نظرا لمحضر مركز إمتحانات **{self.center_ar or ' '}**
يشهد بأنّ   **{enrollment.student.full_name_ar or ' '}**
المولود (ة) في **{enrollment.student.birth_place_ar or ' '} بتأريخ {birth_date} **      
قد استحق (ت) نيل  {exam_translation} العربية
بتقدير**   {distinction_ar}**
لذا منحت له (ها) هذه الشّهادة
وقد جاز (ت) بذلك الحقوق و الإمتيازات المتعلقة بها""")
        self.multi_cell(txt=txt, w=150, markdown=True, h=6.3, align='R')

        self.ln(0)
        self.set_x(self.x + 60)
        self.set_font('Times', 'B', 12)
        self.cell(txt=f'Fait à {self.correction_center} le                             {self.report_date}')
        self.set_x(self.x + 40)
        self.cell(txt=reshape_text(f'حررت في {self.correction_center_ar}  بتأريخ '))

        self.ln()
        self.set_font_size(14)
        self.set_xy(35, self.y + 5)
        self.cell(txt=reshape_text('المعني (ة)'))
        self.set_x(self.x + 55)
        self.cell(txt=reshape_text('نائب مدير الامتحانات والمسابقات'))
        self.set_x(self.x + 35)
        self.cell(txt=reshape_text('توقيع مدير الامتحانات والمسابقات'))
    
    def get_distinction(self, average, french):
        key = str(int(average))
        if french:
            return self.distinctions_french.get(key) or ''
        return self.distinctions_arabic.get(key) or ''


class CentersListPDF(BasePDF):
    def add_content(self, user, queryset, location=None):
        self.add_page()
        self.add_first_page_header()
        
        self.set_margins(5, 5, 5)
        self.set_font('Times', 'B', 12)
        self.set_xy(self.x + 35, self.y+5)

        title = f'LISTE DES CENTRES CHERIFLA '
        if location:
            title += f'DE {location}'
        
        title += f' ({get_current_year()})'
        self.cell(w=225, h=8, txt=title, border=1, align='C')
        self.set_y(self.y + 15)

        self.set_font('Times', 'B', 10)
        self.set_line_width(0.3)
        grayscale = 210
        headings_style = FontFace(emphasis="BOLD", fill_color=grayscale)
        text_aligns = (
            'CENTER', "CENTER", 'CENTER', 'CENTER', 'LEFT', 'CENTER', 'CENTER', 
            'CENTER', 'CENTER', 'CENTER', 'CENTER', 'CENTER', 'CENTER', 'CENTER',
        )
        col_widths = (10, 30, 40, 40, 60, 20, 30, 15, 25, 20)

        with self.table(
            cell_fill_color=grayscale,
            col_widths=col_widths,
            headings_style=headings_style,
            line_height=8,
            text_align=text_aligns,
            width=self.w - 20,
            num_heading_rows=2
        ) as table:
            row = table.row(style=headings_style)
            row.cell('N°', rowspan=2)
            row.cell('LOCALISATION DU CENTRE', colspan=3)
            row.cell("CENTRE", rowspan=2)
            row.cell('CAPACITE DU CENTRE', colspan=2)
            row.cell('ECOLES ET EFFECTIFS', colspan=3)
            
            row = table.row(style=headings_style)
            row.cell('COMMISSION')
            row.cell('DRENA')
            row.cell('IEPP')
            row.cell('Salles')
            row.cell('Capacité')
            row.cell('Ecoles')
            row.cell('Eff attendus')
            row.cell('Eff reçus')

            counter = 1
            for center in queryset:
                row = table.row()
                row.cell(f'{str(counter).zfill(2)}')
                row.cell(str(location or center.school.local_commission))
                row.cell(str(center.school.drena_obj) or center.school.drena)
                row.cell(center.school.iepp)
                row.cell(str(center))
                row.cell(str(center.rooms))
                row.cell(str(center.rooms_capacity or 0))
                row.cell(str(center.schools))
                row.cell(str(center.schools_students_count))
                row.cell(str(center.candidates))
                counter += 1