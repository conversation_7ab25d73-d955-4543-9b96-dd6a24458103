
# GENDER
GENDER_MALE = 'M'
GENDER_FEMALE = 'F'
GENDER_CHOICES = (
    (GENDER_MALE, 'M'),
    (GENDER_FEMALE, 'F'),
)

# HELP_TEXTS IN FORMS
WHATSAPP_NUMBER_HELP_TEXT = 'de préférence, un numéro whatsapp'

# LANGUAGES
LANGUAGE_FRENCH = 'Fr'
LANGUAGE_ARABIC = 'Ar'

# EXAMS
EXAM_CEPE = 'cepe'
EXAM_BEPC = 'bepc'
EXAM_BAC = 'bac'
EXAM_CHOICES = (
    (EXAM_CEPE, 'CEPE'),
    (EXAM_BEPC, 'BEPC'),
    (EXAM_BAC, 'BAC'),
)
EXAMS_LIST = [EXAM_CEPE, EXAM_BEPC, EXAM_BAC]

EXAM_TYPE_LIBRE = 'L'
EXAM_TYPE_OFFICIEL = 'O'
EXAM_TYPES_CHOICES = (
    (EXAM_TYPE_LIBRE, 'LIBRE'),
    (EXAM_TYPE_OFFICIEL, 'OFFICIEL')
)

# USER ROLES
ROLE_ECOLE = 'EC'
ROLE_COMMISSION_LOCALE = 'CL'
ROLE_COMMISSION_NATIONALE = 'CN'
ROLE_DELEGATE = 'DG'

IDENTIFIER_DIGITS_COUNT = 4

COMPRESSED_IMAGE_WIDTH = 500
COMPRESSED_IMAGE_HEIGHT = 500

CERTIFICATE_IMAGE_WIDTH = 700
CERTIFICATE_IMAGE_HEIGHT = 900

STUDENTS_IMAGE_FOLDER = 'images/students'

# NATIONALITIES
NATIONALITY_IVORIAN = 'CI'
NATIONALITY_BURKINA = 'BF'
NATIONALITY_MALI = 'ML'
NATIONALITY_GUINEA = 'GN'
NATIONALITY_SENEGAL = 'SN'
NATIONALITY_OTHER = 'OT'

NATIONALITY_CHOICES = (
    (NATIONALITY_IVORIAN, 'Ivoirienne'),
    (NATIONALITY_BURKINA, 'Burkinabè'),
    (NATIONALITY_GUINEA, 'Guinéenne'),
    (NATIONALITY_MALI, 'Malienne'),
    (NATIONALITY_SENEGAL, 'Senegalaise'),
    (NATIONALITY_OTHER, 'Autre'),
)

# TRANSLATIONS
CEPE_DEGREE_TRANSLATION = 'الشَّهَــــادَةُ الابْتِدَائِيَّةُ'
BEPC_DEGREE_TRANSLATION = 'الشّهــــادة الإعداديّة'
CEPE_TRANSLATION = 'الصف السادس الابتدائي'
BEPC_TRANSLATION = 'الثالث المتوسط'
BAC_TRANSLATION = 'الثالث الثانوي'
CENTER_TRANSLATION = 'مراكز الامتحانات'

DECISIONS_FRENCH = {
    'male_admitted': 'ADMIS',
    'male_declined': 'REFUSE',
    'female_admitted': 'ADMISE',
    'female_declined': 'REFUSEE',
}
DECISIONS_TRANSLATION = {
    'male_admitted': 'ناجح',
    'male_declined': 'راسب',
    'female_admitted': 'ناجحة',
    'female_declined': 'راسبة',
}

EXAMS_DEFINITIONS = {
    'cepe': "CERTIFICAT D'ETUDES PRIMAIRES ELEMENTAIRES",
    'bepc': "BREVET D'ETUDES DU PREMIER CYCLE DU SECONDAIRE",
    'bac': "BACCALAUREAT",
}


EDUCATION_ARABIC = 'A'
EDUCATION_FRENCH = 'F'
EDUCATION_CHOICES = (
    (EDUCATION_ARABIC, 'Arabe'),
    (EDUCATION_FRENCH, 'Français'),
)

CARD_STATUS_PAID = 'PD'
CARD_STATUS_UNPAID = 'UP'
CARD_STATUS_CHOICES = (
    (CARD_STATUS_PAID, 'Payée'),
    (CARD_STATUS_UNPAID, 'Non payée'),
)