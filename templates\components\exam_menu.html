<li class="sidebar-nav-item">
  <!-- First Level -->
  <a href="javascript:void(0)" class="sidebar-nav-link" onclick="toggleSubmenu(this)">
    <span class="sidebar-nav-icon"><i data-feather="users"></i></span>
    <span class="sidebar-nav-name">{{ exam_name|upper }} {{ active_year }}</span>
    <span class="sidebar-nav-end">
      <i data-feather="chevron-right" style="width: 18px; height: 18px;"></i>
    </span>
  </a>

  <!-- Second Level -->
  <ul class="sidebar-nav-submenu" style="display: none;">
    <li class="sidebar-nav-item sidebar-navitem-close-on-click">
      <a href="" hx-get="{% url 'candidates' active_year exam_name %}"
         hx-target="#main-content"
         hx-push-url="{% url 'candidates' active_year exam_name %}"
         class="sidebar-nav-link">
         <span data-feather="user-plus" class="feather-16"></span>
         <span class="sidebar-nav-name ml-2"> Inscriptions</span>
      </a>
    </li>

    <!-- Centers and Rooms Submenu -->
    {% if perms.exams.add_center and not perms.exams.view_results %}
    {% include 'components/centers_submenu.html' %}
    {% endif %}

    <!-- Mock Exam Submenu -->
    {% if perms.exams.edit_mock_exam_grade %}
    <li class="sidebar-nav-item">
      <a href="javascript:void(0)" class="sidebar-nav-link" onclick="toggleSubmenu(this)">
        <span class="sidebar-nav-icon"><i data-feather="edit-3"></i></span>
        <span class="sidebar-nav-name">Examen Blanc (10 %)</span>
        <span class="sidebar-nav-end">
          <i data-feather="chevron-right" style="width: 18px; height: 18px;"></i>
        </span>
      </a>

      <ul class="sidebar-nav-submenu" style="display: none;">
        {% if perms.exams.view_mock_results  %}
        <li class="sidebar-nav-item sidebar-navitem-close-on-click">
          <a href="" hx-get="{% url 'mock_exam_students' %}?exam={{ exam_name }}"
             hx-target="#main-content"
             hx-push-url="{% url 'mock_exam_students' %}?exam={{ exam_name }}"
             class="sidebar-nav-link">
            <span data-feather="edit" class="feather-16"></span>
            <span class="sidebar-nav-name ml-2">Saisie des Notes</span>
          </a>
        </li>
        <li class="sidebar-nav-item sidebar-navitem-close-on-click">
          <a href="" hx-get="{% url 'grades' %}?exam={{ exam_name }}&type=mock"
             hx-target="#main-content"
             hx-push-url="{% url 'grades' %}?exam={{ exam_name }}&type=mock"
             class="sidebar-nav-link">
            <span data-feather="check-circle" class="feather-16"></span>
            <span class="sidebar-nav-name ml-2">Etat des saisies</span>
          </a>
        </li>
        <li class="sidebar-nav-item sidebar-navitem-close-on-click">
          <a href="" hx-get="{% url 'mock_exam_schools' %}?exam={{ exam_name }}"
             hx-target="#main-content"
             hx-push-url="{% url 'mock_exam_schools' %}?exam={{ exam_name }}"
             class="sidebar-nav-link">
            <span data-feather="list" class="feather-16"></span>
            <span class="sidebar-nav-name ml-2">Listing par école</span>
          </a>
        </li>
        <li class="sidebar-nav-item sidebar-navitem-close-on-click">
          <a href="" hx-get="{% url 'mock_exam_import_export' %}?exam={{ exam_name }}"
             hx-target="#main-content"
             hx-push-url="{% url 'mock_exam_import_export' %}?exam={{ exam_name }}"
             class="sidebar-nav-link">
            <span data-feather="refresh-cw" class="feather-16"></span>
            <span class="sidebar-nav-name ml-2">Import / Export</span>
          </a>
        </li>
        {% endif %}
      </ul>
    </li>
    {% endif %}

    <!-- National Exam Submenu -->
    {% if perms.exams.add_grade %}
    <li class="sidebar-nav-item">
      <a href="javascript:void(0)" class="sidebar-nav-link" onclick="toggleSubmenu(this)">
        <span class="sidebar-nav-icon"><i data-feather="award"></i></span>
        <span class="sidebar-nav-name">Examen National</span>
        <span class="sidebar-nav-end">
          <i data-feather="chevron-right" style="width: 18px; height: 18px;"></i>
        </span>
      </a>

      <ul class="sidebar-nav-submenu" style="display: none;">
        {% if perms.exams.view_grade %}
        {% if perms.exams.change_grade %}
        <li class="sidebar-nav-item sidebar-navitem-close-on-click">
          <a href="" hx-get="{% url 'grade_add' %}?exam={{ exam_name }}"
             hx-target="#main-content"
             hx-push-url="{% url 'grade_add' %}?exam={{ exam_name }}"
             class="sidebar-nav-link">
            <span data-feather="edit" class="feather-16"></span>
            <span class="sidebar-nav-name ml-2">Saisie des Notes (90%)</span>
          </a>
        </li>
        <li class="sidebar-nav-item sidebar-navitem-close-on-click">
          <a href="" hx-get="{% url 'grade_add' %}?exam={{ exam_name }}&updating_grade=true"
             hx-target="#main-content"
             hx-push-url="{% url 'grade_add' %}?exam={{ exam_name }}&updating_grade=true"
             class="sidebar-nav-link">
            <span data-feather="edit-2" class="feather-16"></span>
            <span class="sidebar-nav-name ml-2">Modifier notes</span>
          </a>
        </li>
                <li class="sidebar-nav-item sidebar-navitem-close-on-click">
          <a href="" hx-get="{% url 'grade_import' %}?exam={{ exam_name }}"
             hx-target="#main-content"
             hx-push-url="{% url 'grade_import' %}?exam={{ exam_name }}"
             class="sidebar-nav-link">
            <span data-feather="arrow-up" class="feather-16"></span>
            <span class="sidebar-nav-name ml-2">Import notes Excel</span>
          </a>
        </li>
        <li class="sidebar-nav-item sidebar-navitem-close-on-click">
          <a href="" hx-get="{% url 'grade_export' %}?exam={{ exam_name }}"
             hx-target="#main-content"
             hx-push-url="{% url 'grade_export' %}?exam={{ exam_name }}"
             class="sidebar-nav-link">
            <span data-feather="arrow-down" class="feather-16"></span>
            <span class="sidebar-nav-name ml-2">Export notes Excel</span>
          </a>
        </li>
        {% endif %}
        <li class="sidebar-nav-item sidebar-navitem-close-on-click">
          <a href="" hx-get="{% url 'grades' %}?exam={{ exam_name }}&type=national"
             hx-target="#main-content"
             hx-push-url="{% url 'grades' %}?exam={{ exam_name }}&type=national"
             class="sidebar-nav-link">
            <span data-feather="check-circle" class="feather-16"></span>
            <span class="sidebar-nav-name ml-2">Etat des saisies par centre</span>
          </a>
        </li>
        <li class="sidebar-nav-item sidebar-navitem-close-on-click">
          <a href="" hx-get="{% url 'students_grades_status' %}"
             hx-target="#main-content"
             hx-push-url="{% url 'students_grades_status' %}"
             class="sidebar-nav-link">
            <span data-feather="check-circle" class="feather-16"></span>
            <span class="sidebar-nav-name ml-2">Etat des saisies par élève</span>
          </a>
        </li>
        {% endif %}
        {% if perms.exams.view_distinction %}
        <li class="sidebar-nav-item sidebar-navitem-close-on-click">
          <a href="" hx-get="{% url 'distinctions' %}?exam=cepe" 
             hx-target="#main-content"
             hx-push-url="{% url 'distinctions' %}?exam=cepe"
             class="sidebar-nav-link {% if section == 'distinctions' %} active {% endif %}">
            <span class="sidebar-nav-icon"><i data-feather="list"></i></span>
            <span class="sidebar-nav-name">Mentions</span>
            <span class="sidebar-nav-end"></span>
          </a>
        </li>
        {% endif %}
      </ul>
    </li>
    {% endif %}
    {% if perms.exams.view_results %}
    <li class="sidebar-nav-item" style="list-style-type: none;">
      <a href="javascript:void(0)" class="sidebar-nav-link" onclick="toggleSubmenu(this)">
        <span class="sidebar-nav-icon"><i data-feather="file-text"></i></span>
        <span class="sidebar-nav-name">Résultats et Diplomes</span>
        <span class="sidebar-nav-end">
          <i data-feather="chevron-right" style="width: 18px; height: 18px;"></i>
        </span>
      </a>

      <!-- Third Level -->
      <ul class="sidebar-nav-submenu" style="display: none;">
         <li class="sidebar-nav-item sidebar-navitem-close-on-click">
           <a href="" hx-get="{% url 'results_documents' %}?exam={{ exam_name }}&lang=ar"
           hx-target="#main-content"
           hx-push-url="{% url 'results_documents' %}?exam={{ exam_name }}&lang=ar"
              class="sidebar-nav-link">
             <span data-feather="map" class="feather-16"></span>
             <span class="sidebar-nav-name ml-2"> Résultats en arabe</span>
           </a>
         </li>
         <li class="sidebar-nav-item sidebar-navitem-close-on-click">
           <a href="" hx-get="{% url 'results_documents' %}?exam={{ exam_name }}&lang=fr"
           hx-target="#main-content"
           hx-push-url="{% url 'results_documents' %}?exam={{ exam_name }}&lang=fr"
              class="sidebar-nav-link">
              <span data-feather="map" class="feather-16"></span>
              <span class="sidebar-nav-name ml-2"> Résultats en français</span>
           </a>
         </li>
         <li class="sidebar-nav-item sidebar-navitem-close-on-click">
           <a href="" hx-get="{% url 'bulletins_list' %}?exam={{ exam_name }}"
              hx-target="#main-content"
              hx-push-url="{% url 'bulletins_list' %}?exam={{ exam_name }}"
              class="sidebar-nav-link">
              <span data-feather="file" class="feather-16"></span>
              <span class="sidebar-nav-name ml-2"> Attestations et Diplomes</span>
           </a>
         </li>
      </ul>
    </li>
    {% endif %}
    {% if perms.exams.view_studentcorrection and perms.exams.view_results %}
    <li class="sidebar-nav-item sidebar-navitem-close-on-click">
      <a href="" hx-get="{% url 'corrections' exam_name %}"
      hx-target="#main-content"
      hx-push-url="{% url 'corrections' exam_name %}"
         class="sidebar-nav-link">
         <span data-feather="edit-3" class="feather-16"></span>
         <span class="sidebar-nav-name ml-2"> Demandes de correction</span>
      </a>
    </li>
    {% endif %}
    {% if perms.exams.view_results %}
    <li class="sidebar-nav-item" style="list-style-type: none;">
      <a href="javascript:void(0)" class="sidebar-nav-link" onclick="toggleSubmenu(this)">
        <span class="sidebar-nav-icon"><i data-feather="bar-chart"></i></span>
        <span class="sidebar-nav-name">Statistiques</span>
        <span class="sidebar-nav-end">
          <i data-feather="chevron-right" style="width: 18px; height: 18px;"></i>
        </span>
      </a>

      <!-- Third Level -->
      <ul class="sidebar-nav-submenu" style="display: none;">
         <li class="sidebar-nav-item sidebar-navitem-close-on-click">
           <a href="" hx-get="{% url 'grades_stats' %}?exam={{ exam_name }}"
           hx-target="#main-content"
           hx-push-url="{% url 'grades_stats' %}?exam={{ exam_name }}"
              class="sidebar-nav-link">
             <span data-feather="circle" class="feather-16"></span>
             <span class="sidebar-nav-name ml-2"> Statistiques par centre</span>
           </a>
         </li>
         <li class="sidebar-nav-item sidebar-navitem-close-on-click">
           <a href="" hx-get="{% url 'grades_stats_by_location' %}?exam={{ exam_name }}"
           hx-target="#main-content"
           hx-push-url="{% url 'grades_stats_by_location' %}?exam={{ exam_name }}"
              class="sidebar-nav-link">
              <span data-feather="square" class="feather-16"></span>
              <span class="sidebar-nav-name ml-2"> Statistiques par localité</span>
           </a>
         </li>
      </ul>
    </li>
    {% endif %}
  </ul>
</li>
