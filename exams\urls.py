from django.urls import path
from django.contrib.sitemaps.views import sitemap
from exams.sitemaps import HomePageSiteMapView
from . import views, apis, views_celery

urlpatterns = [
     path('', views.HomePageView.as_view(), name='home'),
     path('candidats/<str:exam>/',
          views.CandidatesListView.as_view(), name='candidates'),
     path('candidats/non-valides/', views.unconfirmed_candidates_list_view,
          name='unconfirmed_candidates'),
     path('candidat/<int:pk>/',
          views.CandidateDetailView.as_view(), name='candidate_detail'),
     path('espacecandidat/',
          views.CandidateSpaceView.as_view(), name='candidate_space'),
     path('candidat/<int:pk>/supprimer/',
          views.CandidateDeleteView.as_view(), name='candidate_delete'),
     path('candidat/<str:exam>/nouveau/', views.candidate_edit_view,
          name='candidate_add'),
     path('candidat/<str:exam>/<int:enrollment_id>/', views.candidate_edit_view,
          name='candidate_edit'),
     path('candidat/<str:exam>/<int:pk>/valider/',
          views.CandidateStatusChangeView.as_view(), name='confirm_status'),
     path('candidat/<int:pk>/annuler-candidature/',
          views.CandidateStatusCancelView.as_view(), name='cancel_status'),
     path('valider_candidats/<str:exam>/',
          views.school_candidates_status_change, name='confirm_school_candidates'),
     path('candidat/<int:year>/<int:pk>/actenaissance/',
          views.CandidateCertificateDetailView.as_view(),
          name='candidate_certificate'),
     path('infoscandidat/', views.candidate_infos_check_view, name='candidate_infos'),
     path('infoscandidat/verifier/', views.candidate_infos_view, name='candidate_form'),
]

urlpatterns += [
    path('ecoles/', views.SchoolsListView.as_view(), name='schools'),
    path('ecoles/ajouter/', views.SchoolCreateView.as_view(), name='school_add'),
    path('ecole/<int:pk>/editer/', views.SchoolUpdateView.as_view(), name='school_edit'),
    path('ecole/<int:pk>/reconnaitre/', views.SchoolConfirmView.as_view(), name='school_confirm'),
    path('ecole/infos-drena/', views.send_drena_infos_view, name='send_drena_infos'),
    path('ecoles/centres/', views.SchoolCentersListView.as_view(), name='school_centers'),
    path('ecole/<int:pk>/annuler-centre/<str:exam>/', views.SchoolCenterCancelView.as_view(), name='school_center_cancel'),
    path('ecole/<int:pk>/assigner-centre/<str:exam>/', views.SchoolCenterAssignView.as_view(), name='school_center_assign'),
    path('ecole/centres-localite/', views.location_centers_for_school, name='location_centers_for_school'),
]

urlpatterns += [
    path('commissions/',
         views.LocalCommissionListView.as_view(), name='commissions'),
    path('commissions/ajouter/',
         views.local_commission_edit_view, name='commission_add'),
    path('commissions/<int:commission_id>/editer/',
         views.local_commission_edit_view, name='commission_edit'),
]

urlpatterns += [
    path('parametres/droits_dexamens_annee/<int:pk>/', views.YearPricingUpdateView.as_view(),
         name='pricing_update'),
    path('parametres/messages/<int:pk>/', views.ScrollingMessageUpdateView.as_view(),
         name='message_update'),
]

urlpatterns += [
    path('versements/', views.PaymentsListView.as_view(), name='payments'),
    path('versement/ajouter/', views.PaymentCreateView.as_view(), name='payment_add'),
    path('versement/<int:pk>/editer/', views.PaymentUpdateView.as_view(), name='payment_edit'),
]

urlpatterns += [
    path('droits_ecole/', views.SchoolFeesListView.as_view(), name='school_fees'),
    path('droits_ecole/ajouter/',  views.SchoolFeesCreateView.as_view(), name='school_fees_add'),
    path('droits_ecole/<int:pk>/editer/',  views.SchoolFeesUpdateView.as_view(), name='school_fees_edit')
]

urlpatterns += [
    path('pdf_candidats/<str:exam>/', views.candidates_list_pdf,
         name='candidates_pdf'),
    path('pdf_ecoles/', views.schools_list_pdf, name='schools_pdf'),
    path('pdf_versements/', views.payments_list_pdf, name='payments_pdf'),
    path('pdf_cotisations/', views.fees_list_pdf, name='fees_pdf'),
]

urlpatterns += [
    path('centres/', views.CentersListView.as_view(), name='centers'),
    path('centres/ajouter/', views.CenterCreateView.as_view(), name='center_add'),
    path('centres/<int:pk>/editer/', views.CenterUpdateView.as_view(), name='center_edit'),
    path('centres/<int:pk>/supprimer/', views.CenterDeleteView.as_view(), name='center_delete'),
    path('centres/<int:center_id>/<str:exam>/candidats/', views.center_candidates_list_pdf, name='center_candidates'),
    path('centres/listes/', views.centers_list_pdf, name='centers_list_pdf'),
    path('centres/<int:center_id>/<str:exam>/listes_par_salle/', views.center_rooms_candidates_list_pdf, name='center_rooms'),
    path('centres/<int:location_id>/<str:exam>/', views.confirm_location_centers, name='center_refresh'),
    path('centres/verifier/', views.center_check_view, name='center_check'),
    path('centres/confirmer/', views.center_confirm_view, name='center_confirm'),
    path('centres/statistiques-localites/', views.LocationCenterStatsView.as_view(), name='location_center_stats'),
    path('centres_localite/', views.LocationCentersView.as_view(), name='location_centers'),
    path('ecoles_localite/', views.LocationSchoolsView.as_view(), name='location_schools'),
    path('centres/documents/', views.CenterDocumentsListView.as_view(), name='center_documents'),
]


urlpatterns += [
    path('centres_correction/', views.CorrectionCentersListView.as_view(), name='correction_centers'),
    path('centres_correction/liste_centres/', views.CorrectionCentersCentersListView.as_view(), name='correction_centers_centers'),
    path('centres_correction/centres_localite/', views.LocationCentersForCorrectionView.as_view(), name='correction_centers_location_centers'),
    path('centres_correction/ajouter/', views.CorrectionCenterCreateView.as_view(), name='correction_center_add'),
    path('centres_correction/<int:pk>/editer/', views.CorrectionCenterUpdateView.as_view(), name='correction_center_edit'),
]

urlpatterns += [
    path('permissions/', views.year_permissions_list, name='year_permissions'),
    path('permissions/editer/', views.YearPermissionsUpdateView.as_view(), name='permissions_edit')
]

urlpatterns += [
    path('salles/', views.RoomsListView.as_view(), name='rooms'),
    path('salles/ajouter/', views.RoomsCreateView.as_view(), name='room_add'),
    path('salles/ajouter-en-masse/', views.BatchRoomCreateView.as_view(), name='batch_room_add'),
    path('salles/<int:pk>/editer/', views.RoomsUpdateView.as_view(), name='room_edit'),
    path('salles/<int:pk>/supprimer/', views.RoomDeleteView.as_view(), name='room_delete'),
    path('salles/centres_commission/', views.room_location_centers_view,
         name='room_location_centers'),
]

urlpatterns += [
    path('matieres/', views.SubjectsListView.as_view(), name='subjects'),
    path('matieres/ajouter/', views.SubjectCreateView.as_view(), name='subject_add'),
    path('matieres/<int:pk>/modifier/', views.SubjectUpdateView.as_view(), name='subject_edit'),
]

urlpatterns += [
    path('mentions/', views.DistinctionsListView.as_view(), name='distinctions'),
    path('mentions/ajouter/', views.DistinctionCreateView.as_view(), name='distinction_add'),
    path('mentions/<int:pk>/modifier/', views.DistinctionUpdateView.as_view(), name='distinction_edit'),
]

urlpatterns += [
    path('notes/', views.GradesListView.as_view(), name='grades'),
    path('notes/eleves_sans_notes/', views.StudentsUnmarkedListView.as_view(), name='students_unmarked'),
    path('notes/statut_eleves/', views.StudentsGradesStatusListView.as_view(), name='students_grades_status'),
    path('notes/ajouter/', views.grade_edit_view, name='grade_add'),
    path('progression/', views.center_grading_progress_view, name='grading_progress'),
    path('notes/exportations/', views.grade_export_view, name='grade_export'),
    path('notes/importations/', views.grade_import_view, name='grade_import'),
    path('notes/excel/', views.grades_excel_view, name='grades_excel'),
]

urlpatterns += [
     path('resultats/statistiques_centres/', views.GradesStatisticsByCenterView.as_view(), name='grades_stats'),
     path('resultats/statistiques_localites/', views.GradesStatisticsByLocationView.as_view(), name='grades_stats_by_location'),
     path('resultats/', views.ResultsByCenterListView.as_view(), name='results'),
     path('resultats/documents/', views.ResultsDocumentsByCenterView.as_view(), name='results_documents'),
     path('resultats/pdf/', views.results_list_pdf, name='results_pdf'),
     path('resultats/centre/<int:center_id>/pdf/', views.candidates_results_list_pdf, name='center_results_pdf'),
     path('resultats/repechages/', views.FavouredCandidatesResultsListView.as_view(), name='results_favoured'),
     path('resultats/releves/', views.BulletinByCenterView.as_view(), name='bulletins_list'),
     path('resultats/centre/<int:center_id>/document_pdf/', views.document_pdf, name='document_pdf'),
     path('resultats/convocation_candidat_pdf/', views.candidate_convocation_view, name='convocation_pdf'),
     path('resultats/resultat_candidat/', views.candidate_result_view, name='resultat_candidat'),
]

urlpatterns += [
    path('corrections/<str:exam>/', views.CorrectionsListView.as_view(), name='corrections'),
    path('correction/ajouter/', views.CorrectionCreateView.as_view(), name='correction_add'),
    path('correction/<int:pk>/editer/', views.CorrectionConfirmView.as_view(), name='correction_confirm'),
]

urlpatterns += [
    path('api/', apis.students, name='students_api'),
]


urlpatterns += [
    path('cartes-scolaires/', views.StudentCardListView.as_view(), name='students_cards'),
    path('cartes-scolaires/demande/', views.StudentCardCreateView.as_view(), name='student_card_create'),
    path('cartes-scolaires/demande/<int:pk>/editer/', views.StudentCardUpdateView.as_view(), name='student_card_edit'),
    path('cartes-scolaires/demande/<int:pk>/supprimer/', views.StudentCardDeleteView.as_view(), name='student_card_delete'),
    path('cartes-scolaires/demande/<int:pk>/convertir-en-candidat/', views.StudentCardToCandidateView.as_view(), name='student_card_convert'),
    path('cartes-scolaires/<int:pk>/changer-status-paiement/', views.toggle_card_payment_status, name='student_card_payment_status'),
]

urlpatterns += [
    path('transferts/', views.TransfertListView.as_view(), name='transferts'),
    path('transferts/<int:pk>/confirm/', views.TransfertConfirmView.as_view(), name='transfert_confirm'),
    path('transferts/ajouter/', views.transfert_edit_view, name='transfert_create'),
]

urlpatterns += [
    path('examens-blancs/', views.MockExamListStudentsView.as_view(), name='mock_exam_students'),
    path('examens-blancs/<int:pk>/', views.MockExamGradeEdit.as_view(), name='mock_exam_grade_edit'),
    path('examens-blancs/ecoles/', views.MockExamSchoolsListView.as_view(), name='mock_exam_schools'),
    path('examens-blancs/ecole/<int:school_id>/<str:exam>/pdf/', views.mock_exam_school_candidates_pdf, name='mock_exam_school_pdf'),
    path('examens-blancs/import-export/', views.mock_exam_import_export_view, name='mock_exam_import_export'),
    path('examens-blancs/importation/', views.mock_exam_import_view, name='mock_exam_import'),
    path('examens-blancs/importation/apercu/', views.mock_exam_import_preview_view, name='mock_exam_import_preview'),
    path('examens-blancs/excel/', views.mock_exam_excel_view, name='mock_exam_excel'),
]

urlpatterns += [
    path('drena/', views.LocationListView.as_view(), name='drena'),
    path('drena/nouveau/', views.LocationCreateView.as_view(), name='drena_create'),
    path('drena/<int:pk>/editer/', views.LocationUpdateView.as_view(), name='drena_edit'),
    path('ecoles-drena/', views.get_schools_by_drena, name='get_schools_by_drena'),
]

urlpatterns += [
    path('rh/', views.StaffListView.as_view(), name='staff'),
    path('rh/ajouter/', views.StaffCreateView.as_view(), name='staff_add'),
    path('rh/<int:pk>/editer/', views.StaffUpdateView.as_view(), name='staff_edit'),
]

sitemaps = {
    'accueil': HomePageSiteMapView
}

urlpatterns+= [
    path('sitemap.xml', sitemap, {'sitemaps': sitemaps})
]

# Celery task URLs
urlpatterns += [
    path('download/file/<str:filename>/', views_celery.download_generated_file, name='download_generated_file'),
    path('centres/<int:center_id>/<str:exam>/candidats/async/', views_celery.generate_center_candidates_pdf_async, name='center_candidates_async'),
    path('centres/<int:center_id>/<str:exam>/listes_par_salle/async/', views_celery.generate_center_rooms_pdf_async, name='center_rooms_async'),
    path('centres/listes/async/', views_celery.generate_centers_list_pdf_async, name='centers_list_pdf_async'),
    path('centres/<int:center_id>/<str:exam>/diplomes/async/', views_celery.generate_diplomas_pdf_async, name='diplomas_async'),
    path('centres/<int:center_id>/<str:exam>/attestations/async/', views_celery.generate_attestations_pdf_async, name='attestations_async'),
    path('centres/<int:center_id>/<str:exam>/bulletins/async/', views_celery.generate_bulletins_pdf_async, name='bulletins_async'),
    path('celery/task/<str:task_id>/debug/', views_celery.celery_task_debug, name='celery_task_debug'),
]


