import os
from django.http import FileResponse, Http404, JsonResponse
from django.views.decorators.http import require_GET
from django.contrib.auth.decorators import login_required
from django.shortcuts import render, get_object_or_404
from django.urls import reverse
from . import tasks, models


@login_required
@require_GET
def download_generated_file(request, filename):
    """
    View to download a generated file
    """
    # Construct the file path
    file_path = os.path.join('static', 'pdf', filename)

    # Check if the file exists
    if not os.path.exists(file_path):
        raise Http404("Le fichier demandé n'existe pas.")

    # Determine content type based on file extension
    content_type = 'application/pdf'
    if filename.endswith('.txt'):
        content_type = 'text/plain'

    # Return the file as a response
    return FileResponse(
        open(file_path, 'rb'),
        as_attachment=True,
        content_type=content_type
    )


@login_required
def generate_center_candidates_pdf_async(request, center_id, exam):
    """
    View to generate center candidates PDF asynchronously
    """
    # Get parameters
    print('Center document started')
    document_type = request.GET.get('document')
    room = request.GET.get('salle')

    # Get center information for display
    center = get_object_or_404(models.Center, id=center_id)

    # Start the Celery task
    task = tasks.generate_center_candidates_pdf.delay(
        center_id=center_id,
        exam=exam,
        document_type=document_type,
        room_number=room,
        user_id=request.user.id
    )

    # Prepare the download URL template
    download_url = reverse('download_generated_file', kwargs={'filename': 'FILENAME_PLACEHOLDER'})

    # Determine document type name for display
    document_name = "Liste des candidats"
    if document_type == 'convocations':
        document_name = "Convocations"
    elif document_type == 'fiches_table':
        document_name = "Fiches de table"
    elif document_type == 'fiches_emargement':
        document_name = "Fiches d'émargement"
    elif document_type == 'anonymat':
        document_name = "Fiches d'anonymat"
    elif document_type == 'fiche_oral':
        document_name = "Fiches report oral"
    elif document_type == 'fiche_ecrit':
        document_name = "Fiches report écrit"

    # Return the task ID and download URL template
    print('Center document ok ------------------------ >')
    return render(request, 'partials/celery/task_started_documents.html', {
        'task_id': task.id,
        'download_url_template': download_url,
        'center': center,
        'exam': exam.upper(),
        'document_name': document_name,
        'room_number': room,
    })


@login_required
def generate_center_rooms_pdf_async(request, center_id, exam):
    """
    View to generate center rooms PDF asynchronously
    """
    # Get center information for display
    center = get_object_or_404(models.Center, id=center_id)

    # Start the Celery task
    task = tasks.generate_center_rooms_pdf.delay(
        center_id=center_id,
        exam=exam,
        user_id=request.user.id
    )

    # Prepare the download URL template
    download_url = reverse('download_generated_file', kwargs={'filename': 'FILENAME_PLACEHOLDER'})

    # Return the task ID and download URL template
    return render(request, 'partials/celery/task_started.html', {
        'task_id': task.id,
        'download_url_template': download_url,
        'center': center,
        'exam': exam.upper(),
        'document_name': "Listes par salle",
    })


@login_required
def generate_centers_list_pdf_async(request):
    """
    View to generate centers list PDF asynchronously
    """
    # Get parameters
    exam = request.GET.get('exam')
    location_id = request.GET.get('commission')

    # Get location information if provided
    location = None
    if location_id:
        location = get_object_or_404(models.LocalCommission, id=location_id)

    # Start the Celery task
    task = tasks.generate_centers_list_pdf.delay(
        exam=exam,
        location_id=location_id,
        user_id=request.user.id
    )

    # Prepare the download URL template
    download_url = reverse('download_generated_file', kwargs={'filename': 'FILENAME_PLACEHOLDER'})

    # Return the task ID and download URL template
    return render(request, 'partials/celery/task_started_documents.html', {
        'task_id': task.id,
        'download_url_template': download_url,
        'exam': exam.upper() if exam else '',
        'document_name': "Liste des centres",
        'location': location,
    })


@login_required
def generate_diplomas_pdf_async(request, center_id, exam):
    """
    View to generate diplomas PDF asynchronously
    """
    # Get parameters
    room = request.GET.get('salle')
    table_num = request.GET.get('table_num')

    # Get center information for display
    center = get_object_or_404(models.Center, id=center_id)

    # Start the Celery task
    task = tasks.generate_diplomas_pdf.delay(
        center_id=center_id,
        exam=exam,
        room_number=room,
        table_num=table_num,
        user_id=request.user.id
    )

    # Prepare the download URL template
    download_url = reverse('download_generated_file', kwargs={'filename': 'FILENAME_PLACEHOLDER'})

    # Return the task ID and download URL template
    return render(request, 'partials/celery/task_started_bulletins.html', {
        'task_id': task.id,
        'download_url_template': download_url,
        'center': center,
        'exam': exam.upper(),
        'document_name': "Diplômes",
        'room_number': room,
        'table_num': table_num,
    })


@login_required
def generate_attestations_pdf_async(request, center_id, exam):
    """
    View to generate attestations de bonne conduite PDF asynchronously
    """
    # Get parameters
    room = request.GET.get('salle')
    table_num = request.GET.get('table_num')

    # Get center information for display
    center = get_object_or_404(models.Center, id=center_id)

    # Start the Celery task
    task = tasks.generate_attestations_pdf.delay(
        center_id=center_id,
        exam=exam,
        room_number=room,
        table_num=table_num,
        user_id=request.user.id
    )

    # Prepare the download URL template
    download_url = reverse('download_generated_file', kwargs={'filename': 'FILENAME_PLACEHOLDER'})

    # Return the task ID and download URL template
    return render(request, 'partials/celery/task_started_bulletins.html', {
        'task_id': task.id,
        'download_url_template': download_url,
        'center': center,
        'exam': exam.upper(),
        'document_name': "Attestations de bonne conduite",
        'room_number': room,
        'table_num': table_num,
    })


@login_required
def generate_bulletins_pdf_async(request, center_id, exam):
    """
    View to generate bulletins (relevés de notes) PDF asynchronously
    """
    # Get parameters
    room = request.GET.get('salle')
    table_num = request.GET.get('table_num')
    part = request.GET.get('part')

    # Get center information for display
    center = get_object_or_404(models.Center, id=center_id)

    # Start the Celery task
    task = tasks.generate_bulletins_pdf.delay(
        center_id=center_id,
        exam=exam,
        room_number=room,
        table_num=table_num,
        part=part,
        user_id=request.user.id
    )

    # Prepare the download URL template
    download_url = reverse('download_generated_file', kwargs={'filename': 'FILENAME_PLACEHOLDER'})

    # Return the task ID and download URL template
    return render(request, 'partials/celery/task_started_bulletins.html', {
        'task_id': task.id,
        'download_url_template': download_url,
        'center': center,
        'exam': exam.upper(),
        'document_name': "Bulletins",
        'room_number': room,
        'table_num': table_num,
        'part': part,
    })


@login_required
def celery_task_debug(request, task_id):
    """
    Debug view to check the status of a Celery task
    """
    from celery.result import AsyncResult

    result = AsyncResult(task_id)

    task_status = {
        'task_id': task_id,
        'status': result.status,
        'result': result.result,
        'successful': result.successful(),
        'failed': result.failed(),
        'ready': result.ready(),
    }

    if result.traceback:
        task_status['traceback'] = result.traceback

    return JsonResponse(task_status)
