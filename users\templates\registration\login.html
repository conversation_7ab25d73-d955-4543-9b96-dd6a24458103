{% load widget_tweaks %}
{% load static %}
<style>
  label {
    font-size: .8rem;
    color: dimgray !important;
    font-weight: 500;
  }

  #body, .page-login {
    padding-bottom: 0 !important;
  }

  .page-login {
    padding-top: 0 !important;
  }
  
  .error-message {
    padding: 10px;
    background-color: #ffebee;
    border-left: 4px solid #f44336;
    margin-bottom: 15px;
    border-radius: 4px;
    animation: fadeIn 0.5s ease-in-out;
    display: flex;
    align-items: center;
  }
  
  .error-icon {
    color: #f44336;
    margin-right: 8px;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }
</style>
<div class="adminx-container d-flex justify-content-center align-items-center">
  <div class="page-login">
    <div class="container text-center">
      <img src="{% static 'images/logo.jpg' %}" alt="" width="100px" height="100px"> 
    </div>
    <div class="text-center">
      <a class="navbar-brand mb-4 h1" href="{% url 'home' %}">
        <strong>DIRECTION DES ECOLES CHERIFLA</strong>
      </a>
    </div>

    <div class="card mb-0 shadow-lg rounded">
      <div class="card-header text-center font-weight-bold">
        {% if is_school %}
        <i data-feather="lock" class="text-success"></i> ESPACE ECOLE
        {% elif is_commission_locale%}
        <i data-feather="lock" class="text-success"></i> COMMISSION LOCALE
        {% elif is_commission_nationale %}
        <i data-feather="lock" class="text-success"></i> COMMISSION NATIONALE
        {% elif is_hr %}
        <i data-feather="lock" class="text-success"></i> RESSOURCES HUMAINES
        {% else %}
        <i data-feather="lock" class="text-success"></i> AUTHENTIFICATION
        {% endif %}

      </div>
      <div class="card-body">
        <form action="{% url 'login' %}" method="post" class="material-form" id="loginForm">
          {% csrf_token %}
          <div class="form-group">
            {% if is_school %}
              <label for="{{ form.username.id_for_label }}">CODE ECOLE</label>
              {% render_field form.username class="form-control" type="number" %}
            {% elif is_commission_locale %}
              <label for="{{ form.username.id_for_label }}">LOCALITE</label>
              {% render_field form.username class="form-control"  %}
            {% else %}
              <label for="{{ form.username.id_for_label }}">NOM D'UTILISATEUR</label>
              {% render_field form.username class="form-control"  %}
            {% endif %}
          </div>
          <div class="form-group">
            <label for="{{ form.password.id_for_label }}">MOT DE PASSE</label>
            {% render_field form.password class="form-control" placeholder="Mot de passe" %}
          </div>

          <button type="submit" class="btn btn-block bg-success" id="loginButton">CONNEXION</button>
          
        </form>
      </div>
      <div class="card-footer text-center">
        {% if form.errors %}
          <div class="error-message mb-3">
            <i data-feather="alert-circle" class="error-icon"></i>
            <span><small>Nom d'utilisateur et/ou mot de passe incorrecte</small></span> 
          </div>
        {% endif %}
        <a href="{% url 'espace_choice' %}" class="text-secondary" 
           hx-target="#body"><span data-feather="arrow-left" class="align-middle feather-16"></span> <small>Retour au Menu Espaces</small></a>
      </div>
    </div>
  </div>
</div>

<script>
  if (typeof(feather) !== "undefined") {
    feather.replace()
  }
  
  // Disable login button on form submission
  document.getElementById('loginForm').addEventListener('submit', function(event) {
    // Don't disable button if form has validation errors
    if (this.checkValidity()) {
      const loginButton = document.getElementById('loginButton');
      loginButton.disabled = true;
      loginButton.innerHTML = 'CONNEXION EN COURS...';
    }
  });
  
  // Initialize any error message icons
  document.addEventListener('DOMContentLoaded', function() {
    if (typeof(feather) !== "undefined") {
      feather.replace();
    }
  });
</script>