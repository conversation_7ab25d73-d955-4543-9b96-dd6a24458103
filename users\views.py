from django.views.generic import TemplateView
from django.shortcuts import render
from django.contrib.auth.views import LoginView
from django.contrib.auth import mixins
from django.contrib.auth.models import Group
from django.views import generic
from project_utils import custom_utils
from exams.views import BaseHTMXView
from django.urls import reverse_lazy
from .models import CustomUser
from .forms import CustomAuthenticationForm
from project_utils import constants


class CustomLoginView(LoginView):
    form_class = CustomAuthenticationForm

    def get_template_names(self):
        if bool(self.request.htmx):
            return ['registration/login.html']
        return ['registration/login_full.html']

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_year'] = custom_utils.get_current_year().short_name
        espace = self.request.GET.get('espace') or ''
        espace = espace.lower()
        context['is_school'] = (espace == 'ecole')
        context['is_commission_locale'] = (espace == 'commission_locale')
        context['is_commission_nationale'] = (espace == 'commission_nationale')
        print(espace)
        context['is_hr'] = (espace == 'rh')
        return context

    def form_valid(self, form):
        """Save the selected school year to the session"""
        response = super().form_valid(form)

        # Get the selected school year from the form
        selected_year = form.cleaned_data.get('school_year')
        if selected_year:
            # Save the school year to the session
            self.request.session['selected_year'] = selected_year.short_name

        return response


class EspaceChoiceView(TemplateView):
    def get_template_names(self):
        if bool(self.request.htmx):
            return ['registration/espace_choice.html']
        return ['registration/espace_choice_full.html']
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_year'] = custom_utils.get_current_year().short_name
        return context
    

class CustomUserListView(mixins.PermissionRequiredMixin, BaseHTMXView, generic.ListView):
    permission_required = 'users.view_customuser'
    template_name = 'user_list.html'
    model = CustomUser
    context_object_name = 'users'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_year'] = custom_utils.get_current_year().short_name
        return context
    
    def get_queryset(self):
        return super().get_queryset().filter(
            role=constants.ROLE_DELEGATE, is_staff=False)
    

class CustomUserCreateView(mixins.PermissionRequiredMixin, generic.CreateView):
    permission_required = 'users.add_customuser'
    template_name = 'components/simple_form.html'
    model = CustomUser
    fields = ['last_name', 'first_name', 'gender', 'username', 'locations', 'password']
    success_url = reverse_lazy('user_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['col_width'] = 'col-md-6'
        return context
    
    def form_valid(self, form):
        user = form.save(False)
        user.role = constants.ROLE_DELEGATE
        user.initial_password = form.cleaned_data['password']
        user.set_password(form.cleaned_data['password'])
        user.save()

        # Add user to group 'Délégué pour carte scolaire'
        group = Group.objects.get(name__iexact='délégué')
        user.groups.add(group)


class CustomUserUpdateView(mixins.PermissionRequiredMixin, generic.UpdateView):
    permission_required = 'users.change_customuser'
    template_name = 'components/simple_form.html'
    model = CustomUser
    fields = ['last_name', 'first_name', 'gender', 'locations']
    success_url = reverse_lazy('user_list')