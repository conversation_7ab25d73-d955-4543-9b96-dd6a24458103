<nav class="navbar fixed-bottom shadow d-flex justify-content-around py-0" id="bottom-nav" style="background: white;">
    <a href="#" class="nav-item nav-link bottom-nav-link d-flex flex-column align-items-center {% if section == 'home' %} active {% endif %}"
       hx-get="{% url 'home' %}" hx-target="#main-content" hx-push-url="{% url 'home' %}">
      <i data-feather="home" class="icon" width="18px" height="18px"></i>
      <span>ACCUEIL</span>
    </a>
    <a href="#" 
       class="nav-item nav-link bottom-nav-link d-flex flex-column align-items-center {% if section == 'candidates' and exam == 'CEPE' %} active {% endif %}"
       hx-get="{% url 'candidates' active_year 'cepe' %}"
       hx-target="#main-content" hx-push-url="{% url 'candidates' active_year 'cepe' %}">
        <i data-feather="folder" class="icon" width="18px" height="18px"></i>
        <span>CEPE</span>
    </a>
    <a href="#" class="nav-item nav-link bottom-nav-link d-flex flex-column align-items-center {% if section == 'candidates' and exam == 'BEPC' %} active {% endif %}"
       hx-get="{% url 'candidates' active_year 'bepc' %}"
       hx-target="#main-content" hx-push-url="{% url 'candidates' active_year 'bepc' %}">
        <i data-feather="folder" class="icon" width="18px" height="18px"></i>
        <span>BEPC</span>
    </a>
    <a href="#" class="nav-item nav-link bottom-nav-link d-flex flex-column align-items-center {% if section == 'candidates' and exam == 'BAC' %} active {% endif %}"
      hx-get="{% url 'students_cards' %}?niveau=CM2"
      hx-target="#main-content" hx-push-url="{% url 'students_cards' %}?niveau=CM2">
      <i data-feather="credit-card" class="icon" width="18px" height="18px"></i>
      <span>CARTES S.</span>
    </a>
  </nav>