from io import BytesIO
from typing import Any
from zipfile import ZipFile
from threading import Thread, Lock
import requests
from django import forms
from django.contrib import admin, messages
from django.contrib.admin import SimpleListFilter
from django.db.models import Count, Q, Exists, OuterRef, Subquery
from django.db import transaction
from django.http import HttpRequest, HttpResponse
from import_export.admin import ExportActionModelAdmin, ImportExportActionModelAdmin
from . import models, resources
from .tasks import apply_favour as apply_favour_task
from project_utils import custom_utils, constants
from .tasks import update_annual_average as update_annual_average_task


@admin.register(models.School)
class SchoolAdmin(ImportExportActionModelAdmin):
    search_fields = ['name', 'name_ar']
    list_filter = ['local_commission']
    list_display = [
        'identifier', 'local_commission', 'name', 'name_ar',
        'director', 'phone', 'cepe', 'bepc', 'bac'
        ]

    def cepe(self, school: models.School):
        return school.cepe

    def bepc(self, school: models.School):
        return school.bepc

    def bac(self, school):
        return school.bac

    def get_queryset(self, request):
        self.user = request.user
        # Note: Admin interface doesn't have request session context for year selection
        year = custom_utils.get_current_year()
        return super().get_queryset(request).annotate(
            cepe=Count('enrollment', filter=Q(enrollment__exam=constants.EXAM_CEPE) & \
                       Q(enrollment__year=year)),
            bepc=Count('enrollment', filter=Q(enrollment__exam=constants.EXAM_BEPC) & \
                       Q(enrollment__year=year)),
            bac=Count('enrollment', filter=Q(enrollment__exam=constants.EXAM_BAC) & \
                       Q(enrollment__year=year)),
        )


@admin.register(models.LocalCommission)
class LocalCommissionAdmin(ExportActionModelAdmin):
    list_display = ['location', 'user', 'initial_password']


@admin.register(models.Year)
class YearAdmin(ExportActionModelAdmin):
    list_display = ['name', 'short_name', 'is_current']


@admin.register(models.Student)
class StudentAdmin(ExportActionModelAdmin):
    list_display = [
        'identifier', 'last_name', 'first_name',
        'gender', 'parent_phone', 'school'
    ]
    search_fields = ['identifier', 'last_name', 'first_name']
    list_filter = ['school']
    autocomplete_fields = ['school']


def get_level(exam):
    if exam == constants.EXAM_CEPE:
        return models.StudentCard.LEVEL_CM2
    elif exam == constants.EXAM_BEPC:
        return models.StudentCard.LEVEL_3EME
    elif exam == constants.EXAM_BAC:
        return models.StudentCard.LEVEL_TLE



@admin.register(models.Enrollment)
class EnrollmentAdmin(ExportActionModelAdmin):
    list_display = [
        'year',
        'total', 'mock_average', 'average', 'gen_average', 'favoured',
        'identifier', 'student', 'exam', 'active', 'confirmed',
        'carte', 'center', 'table_num'
    ]
    autocomplete_fields = ['school', 'student']
    list_filter = [
        'year', 'exam', 'confirmed',
        'school__local_commission', 'school',
    ]
    list_select_related = ['student', 'school', 'center__school', 'year']
    search_fields = ['student__name', 'student__identifier', 'student__matricule', 'student__first_name']
    actions = [
        'create_studentcards', 'set_mock_average_to_zero',
        'update_annual_average', 'apply_favour',
        'update_card_photos'
    ]
    resource_class = resources.EnrollmentResource

    def get_queryset(self, request):
        return super().get_queryset(request)\
            .annotate(
                card_status=Subquery(
                    models.StudentCard.objects.filter(enrollment=OuterRef('pk')).only('status').values('status')[:1]
                )
            )

    @admin.action(description='Repêcher les élèves repêchables (tâche en arrière-plan)')
    def apply_favour(self, request, queryset):

        # Get the enrollment IDs
        enrollment_ids = list(queryset.values_list('id', flat=True))

        # Launch the background task
        task = apply_favour_task.delay(enrollment_ids)

        # Inform the admin user
        messages.success(
            request,
            f"Repêchage des élèves lancé en arrière-plan pour {len(enrollment_ids)} inscriptions. "
            f"ID de la tâche: {task.id}"
        )

    @admin.action(description="Calculer moyenne (tâche en arrière-plan)")
    def update_annual_average(self, request, queryset):

        # Get the enrollment IDs
        enrollment_ids = list(queryset.values_list('id', flat=True))

        # Launch the background task
        task = update_annual_average_task.delay(enrollment_ids)

        # Inform the admin user
        messages.success(
            request,
            f"Mise à jour des moyennes annuelles lancée en arrière-plan pour {len(enrollment_ids)} inscriptions. "
            f"ID de la tâche: {task.id}"
        )

    @admin.action(description="Définir la moyenne de l'examen blanc sur 0")
    def set_mock_average_to_zero(self, request, queryset):
        queryset.filter(mock_average__isnull=True).update(mock_average=0)

    # @admin.display(description='carte s.')
    # def get_card_status(self, enrollment):
    #     if enrollment.card_status:
    #         if enrollment.card_status == models.StudentCard.STATUS_PENDING:
    #             return 'EN PRODUCTION'
    #         elif enrollment.card_status == models.StudentCard.STATUS_MANUFACTURED:
    #             return 'EN PRODUCTION'
    #         elif enrollment.card_status == models.StudentCard.STATUS_SHIPPED:
    #             return 'LIVREE'
    #     else:
    #         return 'EN ATTENTE'

    def carte(self, enrollment):
        return enrollment.card_status

    def identifier(self, enrollment):
        return enrollment.student.identifier

    @admin.action(description='Générer cartes scolaires')
    def create_studentcards(modeladmin, request, queryset):
        cards_to_create = []
        queryset = queryset.annotate(
            card_exists=Exists(models.StudentCard.objects.filter(enrollment=OuterRef('pk'))))\
            .select_related('student', 'school')
        queryset = queryset.filter(active=True, card_exists=False)
        for enrollment in queryset:
            print(str(enrollment), 'has no card record yet')
            card_obj = models.StudentCard(
                year=enrollment.year,
                enrollment=enrollment,
                last_name=enrollment.student.last_name,
                first_name=enrollment.student.first_name,
                gender=enrollment.student.gender,
                birth_date=enrollment.student.birth_date,
                birth_place=enrollment.student.birth_place,
                matricule_dsps=enrollment.student.matricule,
                matricule_cherifla=enrollment.student.identifier,
                phone=enrollment.student.student_phone or enrollment.student.parent_phone or '-',
                school=enrollment.school,
                photo=enrollment.student.photo,
                level=get_level(enrollment.exam)
            )
            cards_to_create.append(card_obj)

        models.StudentCard.objects.bulk_create(cards_to_create, batch_size=1000)

    @admin.display(description='Mettre à jour les photos des cartes scolaires')
    def update_card_photos(modeladmin, request, queryset):
        queryset = queryset.select_related('studentcard', 'student') \
            .filter(studentcard__isnull=False, student__photo__isnull=False)

        objs_to_update = []
        for enrollment in queryset:
            photo = enrollment.student.photo
            student_card = enrollment.studentcard
            student_card.photo = photo
            student_card.comment = ''
            objs_to_update.append(student_card)

        models.StudentCard.objects.bulk_update(objs_to_update, fields=['photo', 'comment'])
        messages.success(request, 'Les photos des cartes scolaires ont été mises à jour avec succès.')


@admin.register(models.SchoolPayment)
class PaymentAdmin(ExportActionModelAdmin):
    list_display = ['year', 'school', 'date', 'amount']
    autocomplete_fields = ['school']
    list_filter = ['school', 'year']


@admin.register(models.SchoolFees)
class SchoolFeesAdmin(ExportActionModelAdmin):
    list_display = ['year',  'school', 'date', 'amount']


# class CenterSchoolInline(admin.TabularInline):
#     model = models.School
#     fields = ['identifier', 'name', 'local_commission', 'director', 'phone']
#     extra = 1


def confirm_center_candidates(modeladmin, request, queryset):
    for center in queryset:
        custom_utils.confirm_center(center)

def cancel_attribution(modeladmin, request, queryset):
    for center in queryset:
        center.enrollment_set.update(room=None, table_num=None, anonymat=None)

def cancel_room_attributions(modeladmin, request, queryset):
    for center in queryset:
        center.enrollment_set.update(room=None)

def cancel_grades(modeladmin, request, queryset):
    for center in queryset:
        # Note: Admin actions don't have request session context for year selection
        models.Grade.objects.filter(enrollment__center=center, enrollment__year=custom_utils.get_current_year()).delete()
        center.enrollment_set.update(average=0, favoured=False, total=0, gen_average=0)

confirm_center_candidates.short_description = 'Confirmer les candidats des centres'
cancel_attribution.short_description = 'Annuler les numéros de table, salles et anonymats'
cancel_room_attributions.short_description = 'Annuler les attributions de salles de compos uniquement'
cancel_grades.short_description = 'Annuler les notes du centre'

@admin.register(models.Center)
class CenterAdmin(ExportActionModelAdmin):
    list_display = ['year', 'location', 'identifier', 'school',
                    'correction_center', 'exam', 'complete', 'candidates']
    list_filter = ['exam', 'location', 'year']
    actions = [confirm_center_candidates, cancel_attribution, cancel_room_attributions, cancel_grades]

    def candidates(self, center):
        return center.candidates

    def location(self, center):
        return center.school.location

    def get_queryset(self, request):
        return super().get_queryset(request)\
            .select_related('school', 'correction_center') \
            .annotate(candidates=Count('enrollment', distinct=True))


@admin.register(models.Room)
class RoomAdmin(ExportActionModelAdmin):
    list_display = ['year', 'center', 'number', 'location']
    list_select_related = ['center__school__local_commission', 'center__year']
    list_filter = ['center__location', 'center__year']

    def location(self, room):
        return room.center.school.location

    def year(self, room):
        return room.center.year

class SubjectAdminForm(forms.ModelForm):
    class Meta:
        model = models.Subject
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make location field not required in the admin
        if 'location' in self.fields:
            self.fields['location'].required = False

@admin.register(models.Subject)
class SubjectAdmin(ExportActionModelAdmin):
    form = SubjectAdminForm
    list_filter = ['year', 'exam', 'active', 'group']
    list_display = ['order', 'year', 'exam', 'name', 'translation', 'coefficient', 'active', 'group', 'location']
    search_fields = ['id', 'name', 'translation']

    def get_queryset(self, request):
        return models.Subject.objects.all()


@admin.register(models.Grade)
class GradeAdmin(ExportActionModelAdmin):
    list_display = ['enrollment', 'subject', 'value']
    autocomplete_fields = ['enrollment', 'subject']
    list_filter = ['enrollment__year', 'enrollment__exam']


@admin.register(models.StudentCorrection)
class CorrectionAdmin(ExportActionModelAdmin):
    list_display = [
        'table_num', 'identifier', 'initial_last_name_fr',
        'initial_first_name_fr', 'new_last_name_fr', 'new_first_name_fr',
        'status', 'exam', 'year'
    ]
    list_filter = ['exam', 'year', 'status']


def download_and_add_image(url, file, lock):
    filename = url.split("/")[-1]
    resp = requests.get(url)
    if resp.status_code == 200:
        with lock:  # Acquire lock before writing to ZIP file
            # Convert all images to .jpg format
            original_name = filename
            original_ext = ''
            if '.' in filename and filename.split('.')[-1] != 'jpg':
                original_name, original_ext = filename.split('.')
                filename = f'{original_name}.jpg'
            elif not '.' in filename:
                filename += '.jpg'

            file.writestr(filename, resp.content)

@admin.display(description='Générer les photos en fichier ZIP compressé')
def generate_zip_action(modeladmin, request, queryset):
    # Create a BytesIO object to hold the ZIP file in memory
    zip_buffer = BytesIO()
    queryset = queryset.select_related('enrollment__student', 'school')

    # Create a lock for thread safety
    lock = Lock()

    threads = []
    with ZipFile(zip_buffer, 'w', compresslevel=0) as zip_file:  # Disable compression for faster generation
        for obj in queryset:
            # Define the logic to generate content for each object in the ZIP
            # (e.g., download images, create text files, etc.)
            # Replace this with your specific logic
            if obj.photo:
                url = obj.photo.url  # Replace with your URL generation logic
                thread = Thread(target=download_and_add_image, args=(url, zip_file, lock))
                thread.start()
                threads.append(thread)

        for thread in threads:
            thread.join()

    # Set the content type and filename for the response
    response = HttpResponse(zip_buffer.getvalue(), content_type='application/zip')
    response['Content-Disposition'] = f'attachment; filename={str(queryset.first().school)} PHOTOS.zip'

    return response


class PhotoFilter(SimpleListFilter):
    title = 'Photo Status'  # Display name in the admin filter sidebar
    parameter_name = 'photo_status'  # Query parameter name

    def lookups(self, request, model_admin):
        return [
            ('with_photo', 'Avec Photo'),
            ('without_photo', 'Sans Photo'),
        ]

    def queryset(self, request, queryset):
        if self.value() == 'with_photo':
            return queryset.filter(photo__isnull=False)
        elif self.value() == 'without_photo':
            return queryset.filter(photo__isnull=True)
        return queryset


@admin.register(models.StudentCard)
class StudentCardAdmin(ImportExportActionModelAdmin):
    resource_class = resources.StudentCardResource
    model = models.StudentCard
    list_display = [
        'matricule_dsps', 'matricule_cherifla',
        'last_name', 'first_name', 'gender',
        'birth_date', 'birth_place', 'level',
        'get_status_display', 'date_created',
        'commission', 'school'
    ]
    list_filter = ['year', 'status', 'level', 'school__local_commission', 'school', PhotoFilter]
    search_fields = ['last_name', 'first_name', 'matricule_dsps', 'matricule_cherifla']
    actions = [
                generate_zip_action, 'mark_as_manufactured', 'mark_as_shipped',
               'comment_no_photo_issue_action', 'comment_photo_issue_action'
    ]
    autocomplete_fields = ['school', 'enrollment']

    def commission(self, obj):
        return obj.school.local_commission

    @admin.display(description='Marquer comme imprimées(s)')
    def mark_as_manufactured(modeladmin, request, queryset):
        queryset.filter(photo__isnull=False).update(status=models.StudentCard.STATUS_MANUFACTURED)
        messages.success(request, 'Les objets cartes sélectionnées ont été mentionnées comme <produites> avec succès.')

    @admin.display(description='Marquer comme livrée(s)')
    def mark_as_shipped(modeladmin, request, queryset):
        queryset.update(status=models.StudentCard.STATUS_SHIPPED)
        messages.success(request, 'Les objets cartes sélectionnées ont été mentionnées comme <livrées> avec succès.')

    @admin.display(description='Commenter comme sans photo')
    def comment_no_photo_issue_action(modeladmin, request, queryset):
        queryset.update(comment='Aucune photo')
        messages.success(request, 'Les objets cartes sélectionnées ont été mentionnées comme <sans photo> avec succès.')

    @admin.display(description='Commenter problème de photo')
    def comment_photo_issue_action(modeladmin, request, queryset):
        queryset.update(comment='Photo flou ou mal prise')
        messages.success(request, 'Les objets cartes sélectionnées ont été mentionnées comme <problème de photo> avec succès.')

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'school__drena_obj', 'enrollment__student', 'year',
            'school__local_commission'
        )


@admin.register(models.TransferRequest)
class TransfertAdmin(ExportActionModelAdmin):
    list_display = ['enrollment', 'old_school', 'school', 'confirmed']
    list_select_related = ['enrollment__student', 'old_school', 'school']
    list_filter = ['enrollment__school__local_commission', 'confirmed']
    autocomplete_fields = ['enrollment', 'old_school', 'school']


@admin.register(models.CorrectionCenter)
class CorrectionCenterAdmin(ExportActionModelAdmin):
    list_display = ['year', 'exam', 'correction_center']


@admin.register(models.Location)
class LocationAdmin(ImportExportActionModelAdmin):
    list_display = ['name']
    search_fields = ['name']

@admin.register(models.Staff)
class StaffAdmin(ImportExportActionModelAdmin):
    list_display = [
        'code', 'last_name', 'first_name',
        'birth_date', 'birth_place', 'gender',
        'job_display', 'school'
    ]
    list_filter = ['location', 'status']
    search_fields = ['last_name', 'first_name', 'phone']
    autocomplete_fields = ['location']
    actions = [
        'mark_as_manufactured', 'mark_as_shipped',
        'increment_code_number', 'generate_zip_photos'
    ]
    list_select_related = ['school']
    resource_class = resources.StaffResource

    def iepp(self, staff):
        return staff.school.iepp

    def job_display(self, staff):
        return staff.get_job_display()

    @admin.display(description='Marquer comme imprimées(s)')
    def mark_as_manufactured(modeladmin, request, queryset):
        queryset.update(status=models.StudentCard.STATUS_MANUFACTURED)
        messages.success(request, 'Les objets cartes sélectionnées ont été mentionnées comme <produites> avec succès.')

    @admin.display(description='Marquer comme livrée(s)')
    def mark_as_shipped(modeladmin, request, queryset):
        queryset.update(status=models.StudentCard.STATUS_SHIPPED)
        messages.success(request, 'Les objets cartes sélectionnées ont été mentionnées comme <livrées> avec succès.')

    @admin.display(description='Incrémenter les codes')
    def increment_code_number(modeladmin, request, queryset):
        objs_to_update = []
        for staff in queryset:
            staff_code = staff.code
            digits = int(staff_code[1:])
            digits += 1
            staff.code = f'{digits}'.zfill(3)
            objs_to_update.append(staff)
        models.Staff.objects.bulk_update(objs_to_update, fields=['code'])
        messages.success(request, 'Les codes ont été incrémentés avec succès.')

    @admin.display(description='Générer les photos en fichier ZIP compressé')
    def generate_zip_photos(modeladmin, request, queryset):
        # Create a BytesIO object to hold the ZIP file in memory
        zip_buffer = BytesIO()

        # Create a lock for thread safety
        lock = Lock()

        threads = []
        with ZipFile(zip_buffer, 'w', compresslevel=0) as zip_file:  # Disable compression for faster generation
            for obj in queryset:
                # Define the logic to generate content for each object in the ZIP
                # (e.g., download images, create text files, etc.)
                # Replace this with your specific logic
                if obj.photo:
                    url = obj.photo.url  # Replace with your URL generation logic
                    thread = Thread(target=download_and_add_image, args=(url, zip_file, lock))
                    thread.start()
                    threads.append(thread)

            for thread in threads:
                thread.join()

        # Set the content type and filename for the response
        response = HttpResponse(zip_buffer.getvalue(), content_type='application/zip')
        response['Content-Disposition'] = f'attachment; filename={str(queryset.first().school)} PHOTOS.zip'

        return response