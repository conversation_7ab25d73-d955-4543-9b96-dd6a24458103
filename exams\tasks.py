import os
import time
from celery import shared_task
from celery_progress.backend import ProgressRecorder
from django.conf import settings
from django.core.files.storage import FileSystemStorage
from django.contrib import messages
from project_utils import reports, constants, custom_utils
from . import models
from users.models import CustomUser
from django.db import transaction
from django.db.models import Q
from django.contrib.auth import get_user_model


@shared_task(bind=True)
def generate_center_candidates_pdf(self, center_id, exam, document_type=None, room_number=None, user_id=None):
    """
    Generate PDF for center candidates with progress tracking
    """
    print('Actual task started')
    progress_recorder = ProgressRecorder(self)

    try:
        # Record initial progress
        progress_recorder.set_progress(1, 100, f"Initialisation de la tâche...")

        # Get the center and related data
        center = models.Center.objects.get(id=center_id)
        year = custom_utils.get_current_year()

        progress_recorder.set_progress(5, 100, f"Récupération des données du centre...")

        # Create directory for PDF if it doesn't exist
        pdf_path = os.path.join('static', 'pdf')
        if not os.path.exists(pdf_path):
            os.makedirs(pdf_path)

        # Set up filename
        filename = f'centre_{center_id}_{exam}'
        if document_type:
            filename = f'{filename}_{document_type}'
        if room_number:
            filename = f'{filename}_salle_{room_number}'

        pdf_file = os.path.join(pdf_path, f'{filename}.pdf')

        progress_recorder.set_progress(10, 100, f"Récupération des candidats...")

        # Get candidates
        user = CustomUser.objects.get(id=user_id)
        candidates = models.Enrollment.candidates.get_candidates(
            year=year, exam=exam, user=user
        ).filter(center=center).order_by('table_num')

        # Check document type
        is_fiches_table = document_type == 'fiches_table'
        is_convocation = document_type == 'convocations'
        is_fiches_emarg = document_type == 'fiches_emargement'
        is_anonymat = document_type == 'anonymat'
        is_fiche_oral = document_type == 'fiche_oral'
        is_fiche_ecrit = document_type == 'fiche_ecrit'

        # Filter by room if specified
        if room_number:
            candidates = candidates.filter(room__number=int(room_number))
            progress_recorder.set_progress(15, 100, f"Filtrage des candidats par salle {room_number}...")
        else:
            progress_recorder.set_progress(15, 100, f"Préparation de tous les candidats...")

        # Log the number of candidates
        candidate_count = candidates.count()
        progress_recorder.set_progress(20, 100, f"Préparation de {candidate_count} candidats...")

        # Generate the appropriate PDF based on document type

        if is_fiches_table:
            progress_recorder.set_progress(20, 100, f"Génération des fiches de table...")
            pdf = reports.FicheTable(orientation='P', progress_recorder=progress_recorder, queryset=candidates)
            progress_recorder.set_progress(90, 100, f"Finalisation des fiches de table...")
            pdf.output(pdf_file)
        elif is_convocation:
            progress_recorder.set_progress(20, 100, f"Génération des convocations...")
            candidates = candidates.order_by('school', 'student__last_name', 'student__first_name')
            pdf = reports.Convocation(queryset=candidates, progress_recorder=progress_recorder, orientation='P', unit='mm')
            progress_recorder.set_progress(90, 100, f"Finalisation des convocations...")
            pdf.output(pdf_file)
        elif is_fiches_emarg:
            progress_recorder.set_progress(40, 100, f"Génération des fiches d'émargement...")
            candidates = candidates.order_by('student__last_name', 'student__first_name')
            pdf = reports.CenterCandidatesSignatureListPDF(
                queryset=candidates, exam=exam, year=year,
                orientation='L', unit='mm', room=room_number)
            progress_recorder.set_progress(70, 100, f"Finalisation des fiches d'émargement...")
            pdf.output(pdf_file)
        elif is_fiche_oral:
            progress_recorder.set_progress(40, 100, f"Génération des fiches orales...")
            candidates = candidates.order_by('student__last_name', 'student__first_name')
            pdf = reports.FicheOralePDF(
                queryset=candidates, exam=exam, year=year, room=room_number,
                orientation='L', unit='mm')
            progress_recorder.set_progress(70, 100, f"Finalisation des fiches orales...")
            pdf.output(pdf_file)
        elif is_fiche_ecrit:
            progress_recorder.set_progress(40, 100, f"Génération des fiches écrites...")
            candidates = candidates.order_by('table_num')
            pdf = reports.FicheReportEcrit(
                queryset=candidates, exam=exam, year=year, room=room_number,
                orientation='L', unit='mm')
            progress_recorder.set_progress(70, 100, f"Finalisation des fiches écrites...")
            pdf.output(pdf_file)
        else:
            progress_recorder.set_progress(40, 100, f"Génération de la liste des candidats...")
            pdf = reports.CenterCandidatesListPDF(orientation='L')
            pdf.add_content(
                queryset=candidates, center=center,
                year=year, exam=exam, anonymat=is_anonymat)
            progress_recorder.set_progress(70, 100, f"Finalisation de la liste des candidats...")
            pdf.output(pdf_file)

        # Record progress after PDF generation
        progress_recorder.set_progress(90, 100, f"Document généré avec succès...")

        # Return the file path and other relevant information
        return {
            'file_path': pdf_file,
            'filename': os.path.basename(pdf_file),
            'document_type': document_type or 'liste',
            'center_name': str(center),
            'center_id': center_id,
            'exam': exam
        }
    except Exception as e:
        # Log the error and return it
        import traceback
        error_msg = f"Erreur: {str(e)}\n{traceback.format_exc()}"
        progress_recorder.set_progress(100, 100, f"Erreur: {str(e)}")
        return {
            'error': error_msg,
            'filename': f'error_{center_id}_{exam}.txt'
        }


@shared_task(bind=True)
def generate_center_rooms_pdf(self, center_id, exam, user_id=None):
    """
    Generate PDF for center rooms with progress tracking
    """
    progress_recorder = ProgressRecorder(self)

    try:
        # Record initial progress
        progress_recorder.set_progress(1, 100, f"Initialisation de la tâche...")

        # Get the center and related data
        center = models.Center.objects.get(id=center_id)
        year = custom_utils.get_current_year()

        progress_recorder.set_progress(10, 100, f"Récupération des données du centre...")

        # Create directory for PDF if it doesn't exist
        pdf_path = os.path.join('static', 'pdf')
        if not os.path.exists(pdf_path):
            os.makedirs(pdf_path)

        # Set up filename
        filename = f'centre_salles_{center_id}_{exam}'
        pdf_file = os.path.join(pdf_path, f'{filename}.pdf')

        # Record progress after data preparation
        progress_recorder.set_progress(30, 100, f"Préparation des données des salles...")

        # Get the number of rooms
        room_count = models.Room.objects.filter(center_id=center_id).count()
        progress_recorder.set_progress(40, 100, f"Préparation de {room_count} salles...")

        # Generate the PDF
        progress_recorder.set_progress(50, 100, f"Génération du document en cours...")
        pdf = reports.CenterRoomsCandidatesListPDF(orientation='L')
        pdf.add_content(center=center, year=year, exam=exam)

        progress_recorder.set_progress(80, 100, f"Finalisation du document...")
        pdf.output(pdf_file)

        # Record progress after PDF generation
        progress_recorder.set_progress(90, 100, f"Document généré avec succès...")

        # Return the file path and other relevant information
        return {
            'file_path': pdf_file,
            'filename': os.path.basename(pdf_file),
            'document_type': 'listes_par_salle',
            'center_name': str(center),
            'center_id': center_id,
            'exam': exam
        }
    except Exception as e:
        # Log the error and return it
        import traceback
        error_msg = f"Erreur: {str(e)}\n{traceback.format_exc()}"
        progress_recorder.set_progress(100, 100, f"Erreur: {str(e)}")
        return {
            'error': error_msg,
            'filename': f'error_rooms_{center_id}_{exam}.txt'
        }


@shared_task(bind=True)
def generate_centers_list_pdf(self, exam, location_id=None, user_id=None):
    """
    Generate PDF for centers list with progress tracking
    """
    progress_recorder = ProgressRecorder(self)

    try:
        # Record initial progress
        progress_recorder.set_progress(1, 100, f"Initialisation de la tâche...")

        # Get user and related data
        user = None
        if user_id:
            User = get_user_model()
            user = User.objects.get(id=user_id)
            progress_recorder.set_progress(5, 100, f"Récupération des données de l'utilisateur...")

        year = custom_utils.get_current_year()

        # Create directory for PDF if it doesn't exist
        pdf_path = os.path.join('static', 'pdf')
        if not os.path.exists(pdf_path):
            os.makedirs(pdf_path)

        progress_recorder.set_progress(10, 100, f"Préparation des données...")

        # Set up filename and get centers
        filename = f'centres_cherifla_{exam}'
        location = None

        # Get centers based on user role and location
        progress_recorder.set_progress(20, 100, f"Récupération des centres...")
        centers = models.Center.objects.get_for_year(
            user=user, year=year, exam=exam
        ).order_by('school__local_commission__location',
                'school__drena',
                'school__iepp',
                'school__drena_obj')

        if location_id:
            progress_recorder.set_progress(30, 100, f"Filtrage par localité...")
            location = models.LocalCommission.objects.get(id=location_id)
            centers = centers.filter(location=location)
            filename = f'centres_localite_{location_id}_{exam}'
        elif user and user.role == constants.ROLE_COMMISSION_LOCALE:
            progress_recorder.set_progress(30, 100, f"Filtrage par commission locale...")
            location = user.localcommission
            filename = f'centres_localite_{user.localcommission.id}_{exam}'

        pdf_file = os.path.join(pdf_path, f'{filename}.pdf')

        # Count centers
        center_count = centers.count()
        progress_recorder.set_progress(40, 100, f"Préparation de {center_count} centres...")

        # Generate the PDF
        progress_recorder.set_progress(50, 100, f"Génération du document en cours...")
        pdf = reports.CentersListPDF(orientation='L')
        pdf.add_content(location=location, user=user, queryset=centers)

        progress_recorder.set_progress(80, 100, f"Finalisation du document...")
        pdf.output(pdf_file)

        # Record progress after PDF generation
        progress_recorder.set_progress(90, 100, f"Document généré avec succès...")

        # Return the file path and other relevant information
        return {
            'file_path': pdf_file,
            'filename': os.path.basename(pdf_file),
            'document_type': 'liste_centres',
            'exam': exam,
            'location_name': str(location) if location else 'Tous'
        }
    except Exception as e:
        # Log the error and return it
        import traceback
        error_msg = f"Erreur: {str(e)}\n{traceback.format_exc()}"
        progress_recorder.set_progress(100, 100, f"Erreur: {str(e)}")
        return {
            'error': error_msg,
            'filename': f'error_centers_list_{exam}.txt'
        }


@shared_task
def update_annual_average(enrollment_ids):
    """
    Background task to update annual averages for a list of enrollments

    Args:
        enrollment_ids: List of enrollment IDs to update

    Returns:
        dict: Summary of the operation
    """
    try:
        # Get the enrollments
        enrollments = models.Enrollment.objects.filter(id__in=enrollment_ids)

        if not enrollments.exists():
            return {
                'status': 'error',
                'message': 'No enrollments found with the provided IDs',
                'updated_count': 0
            }

        # Update the annual averages
        objs_to_update = []
        for enrollment in enrollments:
            obj = custom_utils.update_annual_average(enrollment)
            objs_to_update.append(obj)

        # Bulk update the enrollments
        models.Enrollment.objects.bulk_update(
            objs_to_update,
            fields=['gen_average', 'average', 'mock_average']
        )

        return {
            'status': 'success',
            'message': f'Successfully updated {len(objs_to_update)} enrollments',
            'updated_count': len(objs_to_update)
        }
    except Exception as e:
        import traceback
        error_msg = f"Error updating annual averages: {str(e)}\n{traceback.format_exc()}"
        return {
            'status': 'error',
            'message': error_msg,
            'updated_count': 0
        }


@shared_task
def apply_favour(enrollment_ids):
    """
    Background task to apply favour to eligible students

    Args:
        enrollment_ids: List of enrollment IDs to process

    Returns:
        dict: Summary of the operation
    """
    try:

        # Get the enrollments
        queryset = models.Enrollment.objects.filter(id__in=enrollment_ids)

        if not queryset.exists():
            return {
                'status': 'error',
                'message': 'No enrollments found with the provided IDs',
                'updated_count': 0
            }

        year = custom_utils.get_current_year()
        min_avg = year.get_min_avg()
        qs = queryset.filter(
            Q(gen_average__isnull=False) & \
            Q(gen_average__lt=10) & \
            Q(gen_average__gte=min_avg)
        )

        if not qs.exists():
            return {
                'status': 'success',
                'message': 'No eligible enrollments found for favour',
                'updated_count': 0
            }

        points_remaining = 0
        grades_total = 0

        grade_objs_to_update = []
        enrollment_objs_to_update = []

        for enrollment in qs.prefetch_related('grade_set'):
            with transaction.atomic():
                grades_total = enrollment.total

                if enrollment.total < 160:
                    points_remaining = 160 - enrollment.total
                    for grade in enrollment.grade_set.only('value'):
                        grade_value = grade.value
                        if grade_value < 20:
                            diff = 20 - grade_value
                            if points_remaining > 0 and points_remaining > diff:
                                grade.value += diff
                                points_remaining -= diff
                            elif points_remaining > 0 and diff >= points_remaining:
                                grade.value += points_remaining
                                points_remaining = 0

                            grade_objs_to_update.append(grade)
                            grades_total += grade.value
                    enrollment.total = 160
                    enrollment.average = 10
                if enrollment.mock_average != 10:
                    enrollment.mock_average = 10

                enrollment.favoured = True
                enrollment = custom_utils.update_annual_average(enrollment)
                enrollment_objs_to_update.append(enrollment)

        if grade_objs_to_update:
            models.Grade.objects.bulk_update(grade_objs_to_update, fields=['value'])

        if enrollment_objs_to_update:
            models.Enrollment.objects.bulk_update(
                enrollment_objs_to_update, fields=[
                    'total', 'average', 'favoured', 'mock_average',
                    'gen_average'
                ]
            )

        return {
            'status': 'success',
            'message': f'Successfully applied favour to {len(enrollment_objs_to_update)} enrollments',
            'updated_count': len(enrollment_objs_to_update),
            'grades_updated': len(grade_objs_to_update)
        }
    except Exception as e:
        import traceback
        error_msg = f"Error applying favour: {str(e)}\n{traceback.format_exc()}"
        return {
            'status': 'error',
            'message': error_msg,
            'updated_count': 0
        }


@shared_task(bind=True)
def generate_diplomas_pdf(self, center_id, exam, room_number=None, table_num=None, user_id=None):
    """
    Generate PDF for diplomas with progress tracking

    Args:
        center_id: ID of the center
        exam: Exam type (cepe, bepc, bac)
        room_number: Optional room number to filter by
        table_num: Optional table number to filter by
        user_id: Optional user ID for permissions

    Returns:
        dict: Information about the generated file
    """
    progress_recorder = ProgressRecorder(self)

    try:
        # Record initial progress
        progress_recorder.set_progress(1, 100, f"Initialisation de la tâche...")

        # Get user if provided
        user = None
        if user_id:
            User = get_user_model()
            user = User.objects.get(id=user_id)
            progress_recorder.set_progress(5, 100, f"Récupération des données de l'utilisateur...")

        # Get center
        center = models.Center.objects.get(id=center_id)
        year = custom_utils.get_current_year()

        progress_recorder.set_progress(10, 100, f"Récupération des données du centre...")

        # Create directory for PDF if it doesn't exist
        pdf_path = os.path.join('static', 'pdf')
        if not os.path.exists(pdf_path):
            os.makedirs(pdf_path)

        # Set up filename
        filename = f'diplomes_centre_{center_id}_{exam}'
        if room_number:
            filename = f'{filename}_salle_{room_number}'
        if table_num:
            filename = f'diplome_{table_num}'

        pdf_file = os.path.join(pdf_path, f'{filename}.pdf')

        # Get candidates
        queryset = models.Enrollment.candidates.get_candidates(
            user=user, year=year, exam=exam
        ).filter(center=center)

        # Filter by room if specified
        if room_number:
            queryset = queryset.filter(room__number=int(room_number))
            progress_recorder.set_progress(15, 100, f"Filtrage des candidats par salle {room_number}...")

        # Filter by table number if specified
        if table_num:
            queryset = queryset.filter(table_num=table_num)
            progress_recorder.set_progress(15, 100, f"Filtrage du candidat par numéro de table {table_num}...")

        # Filter for eligible students
        min_avg = year.get_min_avg()
        queryset = queryset.filter(Q(gen_average__gte=min_avg) | Q(favoured=True))

        # Log the number of candidates
        candidate_count = queryset.count()
        progress_recorder.set_progress(20, 100, f"Préparation de {candidate_count} diplômes...")

        if candidate_count == 0:
            progress_recorder.set_progress(100, 100, f"Aucun candidat éligible trouvé")
            return {
                'status': 'error',
                'message': 'Aucun candidat éligible trouvé',
                'filename': f'error_diplomes_{center_id}_{exam}.txt'
            }

        # Generate the PDF
        progress_recorder.set_progress(30, 100, f"Génération des diplômes en cours...")

        # Create the PDF with progress tracking
        pdf = reports.Diplome(orientation='L', queryset=queryset, progress_recorder=progress_recorder)

        # Record progress after PDF generation
        progress_recorder.set_progress(90, 100, f"Finalisation des diplômes...")
        pdf.output(pdf_file)

        progress_recorder.set_progress(100, 100, f"Diplômes générés avec succès")

        # Return the file path and other relevant information
        return {
            'file_path': pdf_file,
            'filename': os.path.basename(pdf_file),
            'document_type': 'diplome',
            'center_name': str(center),
            'center_id': center_id,
            'exam': exam,
            'candidate_count': candidate_count
        }
    except Exception as e:
        # Log the error and return it
        import traceback
        error_msg = f"Erreur: {str(e)}\n{traceback.format_exc()}"
        progress_recorder.set_progress(100, 100, f"Erreur: {str(e)}")
        return {
            'error': error_msg,
            'filename': f'error_diplomes_{center_id}_{exam}.txt'
        }


@shared_task(bind=True)
def generate_attestations_pdf(self, center_id, exam, room_number=None, table_num=None, user_id=None):
    """
    Generate PDF for attestations de bonne conduite with progress tracking

    Args:
        center_id: ID of the center
        exam: Exam type (cepe, bepc, bac)
        room_number: Optional room number to filter by
        table_num: Optional table number to filter by
        user_id: Optional user ID for permissions

    Returns:
        dict: Information about the generated file
    """
    progress_recorder = ProgressRecorder(self)

    try:
        # Record initial progress
        progress_recorder.set_progress(1, 100, f"Initialisation de la tâche...")

        # Get user if provided
        user = None
        if user_id:
            User = get_user_model()
            user = User.objects.get(id=user_id)
            progress_recorder.set_progress(5, 100, f"Récupération des données de l'utilisateur...")

        # Get center
        center = models.Center.objects.get(id=center_id)
        year = custom_utils.get_current_year()

        progress_recorder.set_progress(10, 100, f"Récupération des données du centre...")

        # Create directory for PDF if it doesn't exist
        pdf_path = os.path.join('static', 'pdf')
        if not os.path.exists(pdf_path):
            os.makedirs(pdf_path)

        # Set up filename
        filename = f'attestations_centre_{center_id}_{exam}'
        if room_number:
            filename = f'{filename}_salle_{room_number}'
        if table_num:
            filename = f'attestation_{table_num}'

        pdf_file = os.path.join(pdf_path, f'{filename}.pdf')

        # Get candidates
        queryset = models.Enrollment.candidates.get_candidates(
            user=user, year=year, exam=exam
        ).filter(center=center)

        # Filter by room if specified
        if room_number:
            queryset = queryset.filter(room__number=int(room_number))
            progress_recorder.set_progress(15, 100, f"Filtrage des candidats par salle {room_number}...")

        # Filter by table number if specified
        if table_num:
            queryset = queryset.filter(table_num=table_num)
            progress_recorder.set_progress(15, 100, f"Filtrage du candidat par numéro de table {table_num}...")

        # Filter for eligible students
        min_avg = year.get_min_avg()
        queryset = queryset.filter(Q(gen_average__gte=min_avg) | Q(favoured=True))

        # Log the number of candidates
        candidate_count = queryset.count()
        progress_recorder.set_progress(20, 100, f"Préparation de {candidate_count} attestations...")

        if candidate_count == 0:
            progress_recorder.set_progress(100, 100, f"Aucun candidat éligible trouvé")
            return {
                'status': 'error',
                'message': 'Aucun candidat éligible trouvé',
                'filename': f'error_attestations_{center_id}_{exam}.txt'
            }

        # Generate the PDF
        progress_recorder.set_progress(30, 100, f"Génération des attestations en cours...")

        # Create the PDF with progress tracking
        pdf = reports.AttestationBonneConduite(orientation='P', queryset=queryset, progress_recorder=progress_recorder)

        # Record progress after PDF generation
        progress_recorder.set_progress(90, 100, f"Finalisation des attestations...")
        pdf.output(pdf_file)

        progress_recorder.set_progress(100, 100, f"Attestations générées avec succès")

        # Return the file path and other relevant information
        return {
            'file_path': pdf_file,
            'filename': os.path.basename(pdf_file),
            'document_type': 'attestation',
            'center_name': str(center),
            'center_id': center_id,
            'exam': exam,
            'candidate_count': candidate_count
        }
    except Exception as e:
        # Log the error and return it
        import traceback
        error_msg = f"Erreur: {str(e)}\n{traceback.format_exc()}"
        progress_recorder.set_progress(100, 100, f"Erreur: {str(e)}")
        return {
            'error': error_msg,
            'filename': f'error_attestations_{center_id}_{exam}.txt'
        }


@shared_task(bind=True)
def generate_bulletins_pdf(self, center_id, exam, room_number=None, table_num=None, part=None, user_id=None):
    """
    Generate PDF for bulletins (relevés de notes) with progress tracking

    Args:
        center_id: ID of the center
        exam: Exam type (cepe, bepc, bac)
        room_number: Optional room number to filter by
        table_num: Optional table number to filter by
        part: Optional part number (1 or 2) for splitting large rooms
        user_id: Optional user ID for permissions

    Returns:
        dict: Information about the generated file
    """
    progress_recorder = ProgressRecorder(self)

    try:
        # Record initial progress
        progress_recorder.set_progress(1, 100, f"Initialisation de la tâche...")

        # Get user if provided
        user = None
        if user_id:
            User = get_user_model()
            user = User.objects.get(id=user_id)
            progress_recorder.set_progress(5, 100, f"Récupération des données de l'utilisateur...")

        # Get center
        center = models.Center.objects.get(id=center_id)
        year = custom_utils.get_current_year()

        progress_recorder.set_progress(10, 100, f"Récupération des données du centre...")

        # Create directory for PDF if it doesn't exist
        pdf_path = os.path.join('static', 'pdf')
        if not os.path.exists(pdf_path):
            os.makedirs(pdf_path)

        # Set up filename
        filename = f'bulletins_centre_{center_id}_{exam}'
        if room_number:
            filename = f'{filename}_salle_{room_number}'
            if part:
                filename = f'{filename}_part_{part}'
        if table_num:
            filename = f'bulletin_{table_num}'

        pdf_file = os.path.join(pdf_path, f'{filename}.pdf')

        # Get candidates
        queryset = models.Enrollment.candidates.get_candidates(
            user=user, year=year, exam=exam
        ).filter(center=center).order_by('-total')

        # Filter by room if specified
        if room_number:
            queryset = queryset.filter(room__number=int(room_number))
            progress_recorder.set_progress(15, 100, f"Filtrage des candidats par salle {room_number}...")

        # Filter by table number if specified
        if table_num:
            queryset = queryset.filter(table_num=table_num)
            progress_recorder.set_progress(15, 100, f"Filtrage du candidat par numéro de table {table_num}...")

        # Handle part filtering for large rooms
        if part and part.isdigit():
            part = int(part)
            if part == 1:
                queryset = queryset[:15]
                progress_recorder.set_progress(18, 100, f"Sélection de la première partie (1-15)...")
            elif part == 2:
                queryset = queryset[15:]
                progress_recorder.set_progress(18, 100, f"Sélection de la deuxième partie (16+)...")

        # Log the number of candidates
        candidate_count = queryset.count()
        progress_recorder.set_progress(20, 100, f"Préparation de {candidate_count} bulletins...")

        if candidate_count == 0:
            progress_recorder.set_progress(100, 100, f"Aucun candidat trouvé")
            return {
                'status': 'error',
                'message': 'Aucun candidat trouvé',
                'filename': f'error_bulletins_{center_id}_{exam}.txt'
            }

        # Generate the PDF
        progress_recorder.set_progress(30, 100, f"Génération des bulletins en cours...")

        # Create the PDF with progress tracking
        pdf = reports.Bulletin(orientation='P', queryset=queryset, progress_recorder=progress_recorder)

        # Record progress after PDF generation
        progress_recorder.set_progress(90, 100, f"Finalisation des bulletins...")
        pdf.output(pdf_file)

        progress_recorder.set_progress(100, 100, f"Bulletins générés avec succès")

        # Return the file path and other relevant information
        return {
            'file_path': pdf_file,
            'filename': os.path.basename(pdf_file),
            'document_type': 'bulletin',
            'center_name': str(center),
            'center_id': center_id,
            'exam': exam,
            'candidate_count': candidate_count
        }
    except Exception as e:
        # Log the error and return it
        import traceback
        error_msg = f"Erreur: {str(e)}\n{traceback.format_exc()}"
        progress_recorder.set_progress(100, 100, f"Erreur: {str(e)}")
        return {
            'error': error_msg,
            'filename': f'error_bulletins_{center_id}_{exam}.txt'
        }